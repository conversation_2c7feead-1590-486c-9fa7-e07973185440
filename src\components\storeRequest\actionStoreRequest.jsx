import { useTranslation } from "react-i18next";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader } from "reactstrap";
import ActionProduct from "../../pages/store-transaction/action-store-transaction";
import ActionProductRequest from "../../pages/store-request/action-store-request";

const ActionStoreRequest = ({
  openAddModal,
  handelCloseModal,
  selectId,
  isShow,
  refetch,
  setOpenAddModal,
  handelCloseAddModal,
  isStoreRequest,
}) => {
  const { t } = useTranslation();
  return (
    <Modal
      isOpen={openAddModal}
      backdrop="static"
      style={{
        maxWidth: "60vw",
        maxHeight: "80vh",
        overflowY: "auto",
        height: "90vh",
        borderRadius: 10,
      }}
    >
      <ModalHeader
        toggle={() => {
          handelCloseModal();
        }}
      >
        {isStoreRequest
          ? selectId
            ? t("store-request.update_request")
            : t("store-request.make_request")
          : t("common.show") + " " + t("common.request")}
      </ModalHeader>
      <ModalBody>
        {isStoreRequest ? (
          <ActionProductRequest
            isShow={isShow}
            selectId={selectId}
            key={selectId}
            handelCloseAddModal={handelCloseAddModal}
            setOpenAddModal={setOpenAddModal}
            refetch={refetch}
          />
        ) : (
          <ActionProduct
            isShow={isShow}
            selectId={selectId}
            key={selectId}
            handelCloseAddModal={handelCloseAddModal}
            setOpenAddModal={setOpenAddModal}
            refetch={refetch}
          />
        )}
      </ModalBody>
    </Modal>
  );
};

export default ActionStoreRequest;
