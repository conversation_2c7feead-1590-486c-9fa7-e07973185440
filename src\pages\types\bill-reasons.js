import React, { useMemo, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import {
  handleBackendErrors,
  hasPermission,
  truncateText,
} from "../../helpers/api_helper";
import { Can } from "../../components/permissions-way/can";
import toastr from "toastr";
import {
  Button,
  Card,
  CardBody,
  Col,
  Container,
  Input,
  Modal,
  ModalBody,
  ModalFooter,
  ModalHeader,
  Row,
} from "reactstrap";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Link, useLocation } from "react-router-dom";
import TableContainer from "../../components/Common/TableContainer";
import { billReasonsApi } from "../../apis/types/bill-reason/api";
import { useBillReasonsQueries } from "../../apis/types/bill-reason/query";
import ClipLoader from "react-spinners/ClipLoader";
import { useForm } from "react-hook-form";
import CustomSelect from "../../components/Common/Select";
import CustomInput from "../../components/Common/Input";
import { getBillReasonTranslation } from "../../constant/constants";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

export default function BillReason() {
  const [selectId, setSelectId] = useState(null);
  const [openStatsusModal, setOpenStatsusModal] = useState(false);
  const [statusIsActive, setstausIsActive] = useState(false);
  const [openSidebar, setOPenSideBar] = useState(false);
  const [isShow, setIsShow] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [pagination, setPagination] = useState(1);

  const [openStatusModal, setOpenStatusModal] = useState(false);
  const [openAddModal, setOpeAddModal] = useState(false);
  const { t } = useTranslation();
  const { pathname } = useLocation();
  const {
    data: billReasonsData,
    isLoading: isLoadingUsers,
    refetch,
  } = useBillReasonsQueries.useGetAll({
    limit: 10,
    page: pagination,
  });

  const { data: singleBillReasonData, isLoading: isLoadingContractType } =
    useBillReasonsQueries.useGet({
      id: selectId,
    });

  console.log("singleBillReasonData", singleBillReasonData?.result);
  const reasonOptions = [
    { value: 1, label: t("bill_reasons.accepted") },
    { value: 2, label: t("bill_reasons.rejected") },
  ];
  const statusOptions = [
    { value: 1, label: t("common.active") },
    { value: 2, label: t("common.in_active") },
  ];

  const billReasonSchema = () =>
    yup.object().shape({
      title: yup.string().required(t("common.field_required")),
      type: yup
        .object()
        .shape({
          label: yup.string().required(),
          value: yup.number().required(),
        })
        .required(t("common.field_required")),
      status: yup
        .object()
        .shape({
          label: yup.string().required(),
          value: yup.number().required(),
        })
        .required(t("common.field_required")),
    });

  const {
    control,
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    watch,
    setError,
  } = useForm({
    defaultValues: {
      title: "",
      type: reasonOptions[0],
      status: statusOptions[0],
    },
    resolver: yupResolver(billReasonSchema(t)), // ✅ call the function
  });
  const breadcrumbItems = [
    {
      title: t("bill_reasons.bill_reason"),
      link: pathname,
    },
  ];
  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  useEffect(() => {
    if (selectId && singleBillReasonData?.result && !isLoadingContractType) {
      const { title } = singleBillReasonData.result;
      reset({
        title: title,
        type: {
          value: singleBillReasonData?.result.type,
          label: reasonOptions.find(
            (item) => item.value === singleBillReasonData?.result?.type
          )?.label,
        },

        status: {
          value: singleBillReasonData?.result.status,
          label: statusOptions.find(
            (item) => item.value === singleBillReasonData?.result?.status
          )?.label,
        },
      });
    } else {
      reset({
        title: "",
        type: reasonOptions[0],
        status: statusOptions[0],
      });
    }
  }, [selectId, singleBillReasonData?.result, isLoadingContractType]);

  const handelCancel = () => {
    handelCloseSideBar();
    reset({
      title: "",
      type: reasonOptions[0],
      status: statusOptions[0],
    });
  };
  const handleToggleStatus = ({ cellProps }) => {
    if (hasPermission("billReason.toggle") && cellProps.id) {
      setSelectId(cellProps.id);

      setstausIsActive(cellProps.status !== 1);
      setOpenStatusModal(true);
    }
  };
  const handelOpenSideBar = (id) => {
    setSelectId(id);
    setOPenSideBar(true);
  };
  const handelSelectId = (id) => {
    setSelectId(id);
  };
  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const handleCloseModal = () => {
    setSelectId(null);
    setOpenStatusModal(false);
    setOpenDeleteModal(false);
  };
  const handelCloseSideBar = () => {
    setOPenSideBar(false);
    reset({
      title: "",
      type: reasonOptions[0],
      status: statusOptions[0],
    });
    setSelectId(null);
    setIsShow(false);
    refetch();
  };
  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setSelectId(null);
    setstausIsActive(false);
    setOpenStatsusModal(false);
    setIsShow(false);
    setOpeAddModal(false);
    refetch();
  };
  const handleStatusConfirmation = async () => {
    try {
      setIsDeleting(true);
      const response = await billReasonsApi.toggle({ id: selectId });

      if (response?.result?.status) {
        refetch();
        toastr.success(response.message || "Status updated successfully");
      } else {
        throw new Error("Invalid response from server");
      }
    } catch (error) {
      handleBackendErrors({ error, setError });
      console.error("Status toggle failed:", error);
    } finally {
      setIsDeleting(false);
      setOpenStatusModal(false);
    }
  };

  const UpdateFun = async (data) => {
    try {
      const response = await billReasonsApi.update({
        title: data.title,
        type: Number(data.type?.value ?? data.type),
        status: Number(data.status?.value ?? data.status),
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      handelCloseSideBar();
    } catch (error) {
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
  };
  const addFun = async (data) => {
    try {
      const response = await billReasonsApi.create({
        title: data.title,
        type: Number(data.type?.value ?? data.type),
        status: Number(data.status?.value ?? data.status),
      });
      await refetch();
      toastr.success(response.message, "data added successfully");

      handelCLoseModal();
      handelCloseSideBar();
      reset();
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
  };
  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await billReasonsApi.deleteOne({
        id: selectId,
      });
      await refetch();

      toastr.success(response.message, "data deleted successfully");
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
    }
  };
  const columns = useMemo(
    () => [
      {
        Header: "#",
        width: 50,
        accessor: "id_toShow",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.title"),
        width: 50,
        accessor: "title",
        disableFilters: true,
        filterable: false,
      },

      {
        Header: t("common.status"),
        disableFilters: true,
        accessor: (cellProps) => (
          <div className="form-check form-switch">
            <Input
              type="checkbox"
              className="form-check-input"
              checked={cellProps.status === 1}
              onChange={() => handleToggleStatus({ cellProps })}
              disabled={!hasPermission("billReason.toggle")}
            />
          </div>
        ),
      },
      {
        Header: t("common.type"),
        disableFilters: true,
        filterable: false,
        accessor: "type",
      },
      {
        Header: t("common.actions"),
        accessor: (cellProps) => {
          return (
            <div className="d-flex align-items-center gap-2">
              <Can permission={"billReason.update"}>
                <Link
                  to="#"
                  className="text-primary"
                  onClick={() => {
                    if (cellProps.is_default !== 1) {
                      handelOpenSideBar();

                      setSelectId(cellProps.id);
                    }
                  }}
                >
                  {/* <i className="mdi mdi-pencil font-size-18"></i> */}
                  <FaPenToSquare size={14} />
                </Link>
              </Can>
              <Can permission={"billReason.show"}>
                <Link
                  onClick={() => {
                    handelOpenSideBar();
                    handelSelectId(cellProps.id);
                    setIsShow(true);
                  }}
                  className="text-success"
                >
                  {/* <i className=" ri-information-fill font-size-16"></i> */}
                  <MdDeleteSweep size={18} />
                </Link>
              </Can>
              <Can permission={"billReason.destroy"}>
                {cellProps.is_default !== 1 && (
                  <Link
                    onClick={() => {
                      if (cellProps.isDefault !== 1) {
                        handelOpenModal();
                        // handelSelectId(cellProps.id);
                        setSelectId(cellProps.id);
                      }
                    }}
                    to="#"
                    className="text-danger"
                  >
                    {/* <i className="mdi mdi-trash-can font-size-18"></i> */}
                    <FaInfoCircle size={14} />
                  </Link>
                )}
              </Can>
            </div>
          );
        },
        disableFilters: true,
        filterable: false,
      },
    ],
    [t]
  );

  const rowData = useMemo(
    () =>
      billReasonsData?.result
        ?.map((item, index) => ({
          id: item.id,
          title: truncateText({ text: item.title, maxLengthPercent: 0.1 }),
          status: item.status,
          type: getBillReasonTranslation(item.type, t),
          id_toShow: index + 1,
        }))
        ?.reverse() || [],
    [billReasonsData?.result, t]
  );
  return (
    <div className="page-content">
      {" "}
      <Container fluid>
        {" "}
        <Breadcrumbs
          title={t("bill_reasons.bill_reason")}
          breadcrumbItems={breadcrumbItems}
          addTitle={t("common.add") + " " + t("bill_reasons.bill_reason")}
          isAddOptions={true}
          handleOrderClicks={() => {
            setSelectId(null);
            setOPenSideBar(true);
            setIsShow(false);
          }}
          canPermission="billReason.store"
        />
        {/* <Card> */}
        <Card style={{ height: "78vh" }}>
          <CardBody>
            <TableContainer
              hideSHowGFilter={false}
              columns={columns}
              className="custom-header-css table align-middle table-nowrap"
              tableClassName="table-centered align-middle table-nowrap mb-0"
              theadClassName="text-muted table-light"
              data={rowData}
              customHeight={"100%"}
              isLoading={isLoadingUsers}
              // hidePagination
              setPage={setPagination}
              pageCount={billReasonsData?.meta?.last_page}
              currentPage={pagination}
            />
          </CardBody>
        </Card>
        <Modal isOpen={openSidebar} toggle={handelCancel} backdrop="static">
          <ModalHeader toggle={handelCancel}>
            {isShow
              ? t("bill_reasons.bill_reason")
              : selectId
              ? t("common.update") + " " + t("bill_reasons.bill_reason")
              : t("common.add") + " " + t("bill_reasons.bill_reason")}
          </ModalHeader>
          <ModalBody>
            <Row>
              <form onSubmit={handleSubmit(selectId ? UpdateFun : addFun)}>
                {isLoadingContractType ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <>
                    <Row>
                      <Col xs={12}>
                        <div className="mb-4">
                          <CustomInput
                            name="title"
                            control={control}
                            label={t("common.title")}
                            type="text"
                            placeholder={t("common.title")}
                            disabled={isShow}
                            error={errors.title}
                            rules={{ required: t("common.field_required") }}
                          />
                        </div>
                      </Col>

                      <Col xs={12}>
                        <div className="mb-4">
                          <CustomSelect
                            name="type"
                            label={t("common.type")}
                            control={control}
                            options={reasonOptions}
                            isDisabled={isShow}
                            error={errors.type}
                          />
                        </div>
                      </Col>

                      <Col xs={12}>
                        <div className="mb-4">
                          <CustomSelect
                            name="status"
                            control={control}
                            label={t("common.status")}
                            options={statusOptions}
                            isMulti={false}
                            isDisabled={isShow}
                            error={errors.status}
                            rules={{ required: t("common.field_required") }}
                          />
                        </div>
                      </Col>
                    </Row>
                  </>
                )}
                <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                  <Button
                    type="button"
                    color="light"
                    onClick={handelCancel}
                    className="btn-sm "
                    style={{ height: "32px", width: "54px" }}
                  >
                    {t("common.close")}
                  </Button>
                  {!isShow && (
                    <Button
                      color="primary"
                      className="btn-sm waves-effect waves-light primary-button"
                      type="submit"
                      disabled={isSubmitting || !watch("title")}
                    >
                      {isSubmitting ? (
                        <ClipLoader color="white" size={15} />
                      ) : selectId ? (
                        t("common.update")
                      ) : (
                        t("common.add")
                      )}
                    </Button>
                  )}
                </div>
              </form>
            </Row>
          </ModalBody>
        </Modal>
        {openStatusModal && selectId && (
          <Modal isOpen={openStatusModal} backdrop="static">
            <ModalHeader toggle={handleCloseModal}>
              {t("common.Attention")}
            </ModalHeader>
            <ModalBody>
              <p>
                <p>{t("common.delete_text")}</p>
              </p>
              <ModalFooter>
                <Button
                  className="btn-sm"
                  color="light"
                  onClick={handleCloseModal}
                >
                  {t("common.cancel")}
                </Button>
                <Button
                  color="primary"
                  className="btn-sm"
                  onClick={handleStatusConfirmation}
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <ClipLoader color="white" size={15} />
                  ) : (
                    t("common.yes")
                  )}
                </Button>
              </ModalFooter>
            </ModalBody>
          </Modal>
        )}
        <Modal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          backdrop="static"
        >
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.delete")} {t("types.reason.reasons")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button type="button" color="light" onClick={handelCLoseModal}>
                {t("common.close")}
              </Button>
              <Button onClick={DeleteFun} type="button" color="danger">
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.delete")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
      </Container>
    </div>
  );
}
