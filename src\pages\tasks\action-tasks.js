import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { But<PERSON>, Col, Row } from "reactstrap";
import { tasksAPis } from "../../apis/tasks/api";
import { useTranslation } from "react-i18next";
import { delegateQueries } from "../../apis/delegate/query";
import { clientsQueries } from "../../apis/clients/query";
import { allTasksQueries } from "../../apis/tasks/query";
import { tasksQueries } from "../../apis/types/tasks/query";
import {
  formatDateLocalTime,
  formatISOToCustomDate,
  getTranslatedReason,
  handleBackendErrors,
  today,
} from "../../helpers/api_helper";
import useSetSelectOptions from "../../hooks/use-set-select-options";
import CustomSelect from "../../components/Common/Select";
import CustomInput from "../../components/Common/Input";
import toastr from "toastr";
import ClipLoader from "react-spinners/ClipLoader";
const TaskActions = ({
  isShow,
  selectId,
  handelClose,
  refetch,
  taskTransactionId,
}) => {
  const { t, i18n } = useTranslation();
  const taskId = selectId;
  // const taskTransactionId = selectId;
  const schema = yup
    .object({
      task_date: yup
        .string()
        .required(t("common.field_required"))
        .test(
          "is-not-before-today",
          t("common.date_not_before_today"),
          function (value) {
            if (!value) return true;
            const selectedDate = new Date(value);
            const todayDate = new Date();
            todayDate.setHours(0, 0, 0, 0);
            selectedDate.setHours(0, 0, 0, 0);
            return selectedDate >= todayDate;
          }
        ),
      // duo_at: yup
      //   .string()
      //   .required(t("common.field_required"))
      //   .test(
      //     "is-after-task-date",
      //     t("common.duo_after_task_two_days"),
      //     function (value) {
      //       const { task_date } = this.parent;
      //       if (!task_date || !value) return true;
      //       const taskDate = new Date(task_date);
      //       const duoDate = new Date(value);
      //       const minDuoDate = new Date(taskDate);
      //       minDuoDate.setDate(taskDate.getDate() + 2);
      //       return duoDate >= minDuoDate;
      //     }
      //   ),
      client_id: yup
        .object()
        .shape({
          label: yup.string().required(),
          value: yup.number().required(),
        })
        .required(t("common.field_required")),
      delegate_id: yup
        .object()
        .shape({
          label: yup.string().required(),
          value: yup.number().required(),
        })
        .required(t("common.field_required")),
      task_type_id: yup
        .object()
        .shape({
          label: yup.string().required(),
          value: yup.number().required(),
        })
        .required(t("common.field_required")),
    })
    .required();

  const initialSTate = {
    task_date: formatDateLocalTime(today),
    duo_at: formatDateLocalTime(
      new Date(today.getTime() + 2 * 24 * 60 * 60 * 1000)
    ),
    client_id: null,
    task_type_id: null,
    delegate_id: null,
  };

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    setError,
    watch,
    setValue,
    control,
  } = useForm({
    defaultValues: { ...initialSTate },
    resolver: yupResolver(schema),
  });

  const { data, isLoading } = allTasksQueries.useGet({
    id: Number(taskId),
    // enabled: taskTransactionId ? !!taskTransactionId : !!taskId,
  });

  const { data: clients } = clientsQueries.useGetAll({ status: 1 });
  const { data: delegate } = delegateQueries.useGetAll({ status: 1 });
  const { data: taskType } = tasksQueries.useGetAll({});

  useEffect(() => {
    if (taskId && !isLoading && data?.result) {
      reset({
        task_date: formatISOToCustomDate(data.result.task_date),
        duo_at: formatISOToCustomDate(data.result.duo_at),
        client_id: {
          value: data?.result?.client?.id,
          label:
            data?.result?.client?.full_name +
            "/" +
            data?.result?.client?.company_name,
        },
        task_type_id: {
          value: data.result.task_type.id,
          label:
            i18n.language === "eng"
              ? data.result.task_type?.name?.en
              : data.result.task_type?.name?.ar,
        },
        delegate_id: {
          value: data?.result?.delegate?.id,
          label: data?.result?.delegate?.full_name,
        },
      });
    }
  }, [selectId, isLoading, data]);

  useEffect(() => {
    if (!selectId) {
      const taskDate = watch("task_date");
      const taskTypeId = watch("task_type_id");

      if (taskDate && taskTypeId?.value) {
        const selectedTaskType = taskType?.result.find(
          (item) => item.id === Number(taskTypeId.value)
        );

        if (selectedTaskType?.def_duo_days) {
          const currentDate = new Date(taskDate);
          if (!isNaN(currentDate)) {
            currentDate.setDate(
              currentDate.getDate() + selectedTaskType.def_duo_days
            );
            setValue("duo_at", formatISOToCustomDate(currentDate));
          }
        }
      }
    }
  }, [watch("task_date"), watch("task_type_id"), taskType, setValue]);

  useEffect(() => {
    if (watch("client_id")?.value && !selectId) {
      const selectedClient = clients?.result.find(
        (item) => item.id === watch("client_id").value
      );
      if (selectedClient?.delegate) {
        setValue("delegate_id", {
          label: selectedClient.delegate.full_name,
          value: selectedClient.delegate.id,
        });
        toastr.info(t("tasks.client_change"));
      }
    }
  }, [watch("client_id")]);

  const clientOptions = useSetSelectOptions({
    data: clients?.result,
    getOption: (item) => ({
      label: `${item.company_name}/${item.full_name || "---"}`,
      value: item.id,
    }),
  });

  const contractTypesOptions = useSetSelectOptions({
    data: taskType?.result,
    getOption: (item) => ({
      label: i18n.language === "eng" ? item.title.en : item.title.ar,
      value: item.id,
    }),
  });

  const delegateOptions = useSetSelectOptions({
    data: delegate?.result,
    getOption: (item) => ({
      label: item.full_name,
      value: item.id,
    }),
  });

  const getCurrentDateOnlyLocal = (value = Date.now()) => {
    const date = new Date(value);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}T00:00`;
  };

  const fieldsNames = [
    {
      id: 0,
      name: "task_date",
      label: t("tasks.tasks_date"),
      isRequired: true,
      type: "datetime-local",
      error: errors.task_date,
      min: getCurrentDateOnlyLocal(),
    },
    {
      id: 1,
      name: "duo_at",
      label: t("tasks.deadLine_task"),
      type: "datetime-local",
      isRequired: false,
      error: errors.duo_at,
      min: watch("task_date")
        ? getCurrentDateOnlyLocal(new Date(watch("task_date")))
        : getCurrentDateOnlyLocal(new Date()),
    },
  ];

  const UpdateFun = async (data) => {
    try {
      const dataForSend = {
        ...data,
        duo_at: formatISOToCustomDate(data.duo_at),
        task_date: formatISOToCustomDate(data.task_date),
        client_id: data.client_id.value,
        delegate_id: data.delegate_id.value,
        task_type_id: Number(data.task_type_id.value),
      };
      const response = await tasksAPis.update({
        payload: dataForSend,
        id: taskTransactionId,
      });
      handelClose();
      toastr.success(response.message);
      refetch();
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
  };

  const addFun = async (data) => {
    try {
      const dataForSend = {
        ...data,
        duo_at: formatISOToCustomDate(data.duo_at),
        task_date: formatISOToCustomDate(data.task_date),
        client_id: data.client_id.value,
        delegate_id: data.delegate_id.value,
        task_type_id: Number(data.task_type_id.value),
      };
      const response = await tasksAPis.add({ payload: dataForSend });
      toastr.success(response.message);
      handelClose();
      refetch();
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
  };

  return (
    <div>
      <h1 style={{ fontSize: 16 }} className="mb-4">
        {isShow
          ? t("common.show") + " " + t("common.tasks")
          : selectId
          ? t("common.update") + " " + t("common.tasks")
          : t("common.add") + " " + t("common.tasks")}
      </h1>
      {isLoading ? (
        <div className="container-loading">
          <ClipLoader color="#ddd" size={50} />
        </div>
      ) : (
        <Row className="g-3">
          <Col xs={6}>
            <CustomSelect
              control={control}
              error={errors.client_id}
              label={t("common.client")}
              options={clientOptions}
              name="client_id"
              isDisabled={isShow}
            />
          </Col>

          <Col xs={6}>
            <CustomSelect
              control={control}
              error={errors.task_type_id}
              label={t("tasks.task_type")}
              options={contractTypesOptions}
              name="task_type_id"
              isDisabled={isShow}
            />
          </Col>

          {fieldsNames.map((field) => (
            <Col xs={6} key={field.id}>
              <CustomInput
                control={control}
                error={field.error}
                min={field.min}
                isDisabled={isShow}
                placeholder={field.label}
                type={field.type}
                name={field.name}
              />
            </Col>
          ))}
          <Col xs={12}>
            <CustomSelect
              control={control}
              error={errors.delegate_id}
              label={t("common.delegate")}
              options={delegateOptions}
              name="delegate_id"
              isDisabled={isShow}
            />
          </Col>
        </Row>
      )}
      {isShow && data?.result?.task_transactions && (
        <div className="mt-4">
          <h5 style={{ marginBottom: 14 }}>{t("tasks.task_transaction")}:</h5>
          <div style={{ display: "grid", gap: 24 }}>
            {data.result.task_transactions.map((item, index) => (
              <li
                key={item.id}
                style={{
                  display: "flex",
                  gap: 8,
                  position: "relative",
                  alignItems: "center",
                  width: "fit-content",
                }}
              >
                <span
                  style={{
                    background: "#4955b3",
                    color: "#fff",
                    borderRadius: "100%",
                    width: 25,
                    fontWeight: "bold",
                    height: 25,
                    display: "flex",
                    fontSize: 12,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  className="steps"
                >
                  {index + 1}
                </span>
                <span>
                  {t(
                    `types.reason.${getTranslatedReason({
                      value: item.status ?? "",
                    })}`
                  )}
                </span>
                {item?.updated_by && (
                  <span>
                    {t("tasks.updated_by")} :{" "}
                    {index === 0 ? item.created_by : item.updated_by}
                  </span>
                )}
                {item?.delegate && (
                  <span>
                    {t("common.delegate_name")} : {item?.delegate?.full_name}
                  </span>
                )}
              </li>
            ))}
          </div>
        </div>
      )}
      <div className="d-flex justify-content-end gap-2 mt-3">
        <Button color="light" onClick={handelClose} className="btn-sm">
          {t("common.cancel")}
        </Button>
        {!isShow && (
          <Button
            color="primary"
            onClick={handleSubmit(selectId ? UpdateFun : addFun)}
            disabled={isSubmitting}
            className="btn-sm"
          >
            {isSubmitting ? (
              <span className="spinner-border spinner-border-sm" />
            ) : selectId ? (
              t("common.update")
            ) : (
              t("common.add")
            )}
          </Button>
        )}
      </div>
    </div>
  );
};

export default TaskActions;
