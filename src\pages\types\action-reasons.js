import { <PERSON><PERSON>, <PERSON>, CardB<PERSON>, Col, Container, Label, Row } from "reactstrap";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { Controller, useForm } from "react-hook-form";
import { useEffect, useState } from "react";
import toastr from "toastr";
import { reasonsTypesQueries } from "../../apis/types/resons/query";
import { reasonsTypesAPis } from "../../apis/types/resons/api";
import ClipLoader from "react-spinners/ClipLoader";
import { delegateQueries } from "../../apis/delegate/query";
import Select from "react-select";
import CustomInput from "../../components/Common/Input";
import CustomSelect from "../../components/Common/Select";

const ActionReasons = ({
  selectId,
  isShow,
  handelCLoseModal,
  refetch,
  has,
}) => {
  const { data: delegates } = delegateQueries.useGetAll({ status: 1 });
  const { data: Reasons } = reasonsTypesQueries.useGetAll({});
  const [optionGroup, setOptionGroup] = useState([]); // Assuming you have a way to populate this
  const [optionReasons, setOptionReasons] = useState([]); // Assuming you have a way to populate this

  const { t, i18n } = useTranslation();
  const { data, isLoading } = reasonsTypesQueries.useGet({
    id: Number(selectId),
  });

  const { data: reasons_parent } = reasonsTypesQueries.useGetAll({ parent: 1 });

  const navigate = useNavigate();

  const handelCancel = () => {
    // navigate("/reasons");
    handelCLoseModal();
    reset();
  };

  const schema = yup
    .object({
      titleArabic: yup.string().required(t("common.field_required")),
      titleEnglish: yup.string().required(t("common.field_required")),
      reason_id: has
        ? null
        : !data?.is_default && !selectId
        ? null
        : yup
            .object()
            .shape({
              label: yup.string().required(t("common.field_required")),
              value: yup.number().required(t("common.field_required")),
            })
            .required(t("common.field_required")),
    })
    .required();

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    control,
    setError,
    setValue,
    watch,
    clearErrors,
  } = useForm({
    defaultValues: {
      titleArabic: "",
      titleEnglish: "",
      status: 1,
      type: 1,
      reason_id: null,
      delegate: 0,
      color: null,
    },
    resolver: yupResolver(schema),
  });

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoading && data?.result) {
      // Populate form with role data when loaded

      // Set the options for the select dropdown
      //   setOptionGroup(clientsToReset);

      reset({
        titleArabic: data?.result?.title.ar || "",
        titleEnglish: data?.result?.title.en || "",
        status: data?.result?.status,
        type: data?.result?.creating_type,
        color: data?.result?.color,
        // delegate: clientsToReset,
      });
    }
  }, [selectId, isLoading, data, reset, Reasons]);

  // Resetting the select field value when data changes
  useEffect(() => {
    // Ensure Reasons and data.result are defined before proceeding
    if (Reasons?.result && data?.result?.parent?.id) {
      const reasonsToReset = Reasons.result.find(
        (item) => item.id === data.result.parent.id
      );

      if (reasonsToReset) {
        // Set the value of reason_id in the form to match the parent id
        setValue("reason_id", {
          label:
            i18n.language === "eng"
              ? reasonsToReset.title?.en
              : reasonsToReset.title?.ar,
          value: reasonsToReset.id,
        });
      }
    }
  }, [Reasons, data, setValue]);

  useEffect(() => {
    if (delegates?.result?.length > 0) {
      setOptionGroup((prev) => [
        ...prev,
        ...delegates?.result?.map((item) => ({
          label: item.full_name,
          value: item.id,
        })),
      ]);
    }
  }, [delegates?.result?.length]);

  useEffect(() => {
    if (reasons_parent?.result?.length > 0) {
      const filteredOptions = reasons_parent.result.map((item) => ({
        label:
          i18n.language === "eng" ? item.title?.en : item.title?.ar || "---",
        value: item.id,
      }));

      setOptionReasons(filteredOptions);
    }
  }, [reasons_parent?.result]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };
  // Convert data to FormData and send it
  const UpdateFun = async (data) => {
    try {
      clearErrors();
      const response = await reasonsTypesAPis.update({
        title: {
          en: data.titleEnglish ? data.titleEnglish : data.titleArabic,
          ar: data.titleArabic ? data.titleArabic : data.titleEnglish,
        },
        status: data.status,
        delegate: data?.delegate?.value,
        color: data.color,
        reason_id: data?.reason_id?.value,
        id: Number(selectId),
      });
      refetch();
      handelCLoseModal();
      toastr.success(response.message);
      if (has) {
        navigate("/tas-reasons");
      } else {
        navigate("/reasons");
      }
      reset();
    } catch (error) {
      handleBackendErrors(error);
    }
  };

  const addFun = async (data) => {
    try {
      clearErrors();
      const response = await reasonsTypesAPis.add({
        title: {
          en: data.titleEnglish ? data.titleEnglish : data.titleArabic,
          ar: data.titleArabic ? data.titleArabic : data.titleEnglish,
        },
        status: data.status,
        delegate: data?.delegate?.value,
        reason_id: data?.reason_id?.value,
        color: data.color,
      });
      toastr.success(response.message);
      handelCLoseModal();
      refetch();
      if (has) {
        navigate("/tas-reasons");
      } else {
        navigate("/reasons");
      }
      reset(); // Reset form after successful submission
    } catch (error) {
      handleBackendErrors(error);
    }
  };

  const handleBackendErrors = (error) => {
    if (error.response?.data?.errors) {
      Object.keys(error.response.data.errors).forEach((field) => {
        const errorMessages = error.response.data.errors[field];
        errorMessages.forEach((errorMessage) => {
          if (errorMessage.includes("(ar)")) {
            setError("titleArabic", {
              type: "manual",
              message: errorMessage,
            });
          } else if (errorMessage.includes("(en)")) {
            setError("titleEnglish", {
              type: "manual",
              message: errorMessage,
            });
          } else {
            // For other fields (if there are any other errors)
            setError(field, {
              type: "manual",
              message: errorMessage,
            });
          }
        });
      });
      toastr.error("There are errors");
      console.error("error", error);
    } else {
      // Fallback for any unexpected errors
      toastr.error("An unexpected error occurred");
      console.error("Unexpected error:", error);
    }
  };

  return (
    <>
      <Container fluid>
        <>
          <CardBody>
            <form
              onSubmit={
                selectId ? handleSubmit(UpdateFun) : handleSubmit(addFun)
              }
            >
              {isLoading ? (
                <div className="container-loading">
                  <ClipLoader color="#ddd" size={50} />
                </div>
              ) : (
                <>
                  <Row>
                    <Col xs={12}>
                      <div className="mb-4">
                        <CustomInput
                          name="titleEnglish"
                          control={control}
                          label={t("common.title_in_english")}
                          type="text"
                          placeholder={t("common.title_in_english")}
                          disabled={isShow}
                          error={errors.titleEnglish}
                          rules={{ required: t("common.field_required") }}
                        />
                      </div>
                    </Col>

                    <Col xs={12}>
                      <div className="mb-4">
                        <CustomInput
                          name="titleArabic"
                          control={control}
                          label={t("common.title_in_arabic")}
                          type="text"
                          placeholder={t("common.title_in_arabic")}
                          disabled={isShow}
                          error={errors.titleArabic}
                        />
                      </div>
                    </Col>
                  </Row>
                  <div className="mb-4">
                    <CustomInput
                      name="color"
                      control={control}
                      label={t("common.color")}
                      type="color"
                      disabled={isShow}
                    />
                  </div>
                  <Row>
                    {((data && !data?.result?.is_default) || !selectId) && (
                      <Col xs={12}>
                        <div className="mb-4">
                          <CustomSelect
                            name="reason_id"
                            control={control}
                            label={t("common.select_reason")}
                            options={optionReasons}
                            isMulti={false}
                            disabled={isShow}
                            menuHeight={80}
                            defaultValue={[]}
                            error={errors.reason_id}
                            isDisabled={isShow}
                          />
                        </div>
                      </Col>
                    )}
                  </Row>
                </>
              )}
              <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                <Button
                  type="button"
                  color="light"
                  onClick={handelCancel}
                  className="btn-sm "
                  style={{ height: "32px", width: "54px" }}
                >
                  {t("common.close")}
                </Button>
                {!isShow && (
                  <Button
                    color="primary"
                    className="btn-sm waves-effect waves-light primary-button "
                    type="submit"
                    disabled={
                      isSubmitting ||
                      (!watch("titleArabic") && !watch("titleEnglish"))
                    }
                  >
                    {isSubmitting ? (
                      <ClipLoader color="white" size={15} />
                    ) : selectId ? (
                      t("common.update")
                    ) : (
                      t("common.add")
                    )}
                  </Button>
                )}
              </div>
            </form>
          </CardBody>
        </>
      </Container>
    </>
  );
};

export default ActionReasons;
