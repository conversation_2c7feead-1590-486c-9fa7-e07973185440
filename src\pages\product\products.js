import ClipLoader from "react-spinners/ClipLoader";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Container,
  Input,
  Modal,
  ModalBody,
  <PERSON><PERSON><PERSON><PERSON>er,
  ModalHeader,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import { useMemo, useState } from "react";
import { productQueries } from "../../apis/products/query";
import toastr from "toastr";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { productAPis } from "../../apis/products/api";
import { useTranslation } from "react-i18next";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { handleBackendErrors, hasPermission } from "../../helpers/api_helper";
import { Can } from "../../components/permissions-way/can";
import DeleteModal from "../../components/Common/DeleteModal";
import SearchCard from "../../components/Reports/search-card";
import { useForm } from "react-hook-form";
import useSetSelectOptions from "../../hooks/use-set-select-options";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

const ProductsList = () => {
  const { t, i18n } = useTranslation();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  const { pathname } = useLocation();

  const [selectId, setSelectId] = useState(null);
  const navigate = useNavigate();
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [openStatsusModal, setOpenStatsusModal] = useState(false);
  const [statusIsActive, setstausIsActive] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setSelectId(null);
    setstausIsActive(false);
    setOpenStatsusModal(false);
  };

  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };

  const handelSelectId = (id) => {
    setSelectId(id);
  };

  const handelAddBonds = () => {
    navigate("/action-products");
  };
  const [searchParams, setSearchParams] = useState({});

  const { control, reset, register, watch, setValue } = useForm({
    defaultValues: {},
  });

  const { data, isLoading, refetch } = productQueries.useGetAll({
    limit: pageSize,
    page: currentPage,
    searchParams,
  });

  const { data: ProductsTypes } = productQueries.useGetAllProductTypes({
    status: 1,
  });

  const searchFields = watch(["product_type_ids", "name"]);

  // Add a manual search function instead of relying on the useEffect
  const handleSearch = () => {
    const params = {};

    // Check if delegates multi-select has values
    if (searchFields[0]) {
      // Handle both single object and array of objects
      if (Array.isArray(searchFields[0])) {
        if (searchFields[0].length > 0) {
          // Create separate parameters for each delegate ID
          searchFields[0].forEach((delegate) => {
            if (!params["filter[product_type_ids][]"]) {
              params["filter[product_type_ids][]"] = [delegate.value];
            } else {
              params["filter[product_type_ids][]"].push(delegate.value);
            }
          });
        }
      } else if (searchFields[0].value) {
        // Single object case
        params["filter[product_type_ids][]"] = [searchFields[0].value];
      }
    }

    if (searchFields[1]) {
      params["filter[name]"] = searchFields[1];
    }

    // Update search params
    setSearchParams(params);
    // Update URL with new filters
    updateUrlWithFilters(params);
    // Reset to first page
    setCurrentPage(1);
    // Trigger a refetch with new parameters
    refetch();
  };

  // Update URL with current filters
  const updateUrlWithFilters = (params) => {
    const newUrl = new URLSearchParams();

    // Add delegate IDs to URL
    if (params["filter[product_type_ids][]"]) {
      const product_type_ids = Array.isArray(
        params["filter[product_type_ids][]"]
      )
        ? params["filter[product_type_ids][]"]
        : [params["filter[product_type_ids][]"]];

      product_type_ids.forEach((id) => newUrl.append("product_type_ids[]", id));
    }

    // Add KM filters to URL
    if (params["filter[name]"]) {
      newUrl.set("name", params["filter[name]"]);
    }

    // Replace current URL without reloading the page
    navigate(`${pathname}?${newUrl.toString()}`, { replace: true });
  };

  const handleReset = () => {
    reset({ name: "", product_type_ids: null });
    setSearchParams({});
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const breadcrumbItems = [
    {
      title: t("products.products"),
      link: pathname,
    },
  ];

  const handelToggleStatus = ({ cellProps }) => {
    if (cellProps.status === 1 && hasPermission("product.disactivate")) {
      setSelectId(cellProps.id);
      setOpenStatsusModal(true);
      setstausIsActive(false);
    } else if (hasPermission("product.activate")) {
      setSelectId(cellProps.id);
      setOpenStatsusModal(true);
      setstausIsActive(true);
    }
  };

  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.name"),
      accessor: "name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.group"),
      accessor: "product_type",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.quant"),
      accessor: "quant",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.price"),
      accessor: "price",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.status"),
      disableFilters: true,
      filterable: false,
      accessor: (cellProps) => {
        return (
          <div className="form-check form-switch">
            <Input
              type="checkbox"
              // disabled={currentUserEmail === cellProps.email}
              className="form-check-input"
              // defaultChecked={cellProps.status === 1 ? true : false}
              checked={cellProps.status === 1}
              onClick={() => handelToggleStatus({ cellProps })}
            />
            <></>
          </div>
        );
      },
    },

    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2">
            <Can permission={"product.update"}>
              <Link
                to={
                  cellProps.is_default !== 1 &&
                  `/action-products?id=${cellProps.id}`
                }
                className="text-primary"
                onClick={() => {}}
              >
                {/* <i className="mdi mdi-pencil font-size-18"></i> */}
                <FaPenToSquare size={14} />
              </Link>
            </Can>
            <Can permission={"product.destroy"}>
              <Link
                onClick={() => {
                  handelOpenModal();
                  handelSelectId(cellProps.id);
                }}
                to="#"
                className="text-danger"
              >
                {/* <i className="mdi mdi-trash-can font-size-18"></i> */}
                <MdDeleteSweep size={18} />
              </Link>
            </Can>
            <Can permission={"product.show"}>
              <Link
                to={`/action-products?id=${cellProps.id}?Show=true`}
                className="text-success"
              >
                {/* <i className=" ri-information-fill font-size-16"></i> */}
                <FaInfoCircle size={14} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const ActiveUser = async (id) => {
    try {
      setIsDeleting(true);
      const response = await productAPis.active({
        id: id,
      });
      refetch();
      toastr.success(response.message);
      handelCLoseModal();
      setIsDeleting(false);
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };
  const InActive = async (id) => {
    try {
      setIsDeleting(true);
      const response = await productAPis.inActive({
        id: id,
      });
      refetch();
      handelCLoseModal();
      setIsDeleting(false);
      toastr.success(response.message);
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  const rowData = useMemo(
    () =>
      data?.result?.length > 0
        ? data.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: (currentPage - 1) * 10 + index + 1, // 10 is your page size
              name: item.name || "----",
              quant: item.quant || 0,
              product_unit:
                i18n.language === "ar"
                  ? item.product_unit.title?.ar
                  : item.product_unit.title?.en || 0,
              product_type:
                i18n.language === "ar"
                  ? item.product_type.title?.ar
                  : item.product_type.title?.en || 0,
              price: item.price || 0,
              icon: item.icon || "---",
              status: item.status,
            }))
            .reverse()
        : [],
    [data?.result, t, currentPage, pageSize]
  );

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await productAPis.deleteFu({
        id: selectId,
      });
      toastr.success(response?.message);
      setIsDeleting(false);
      refetch();
      setOpenDeleteModal(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      console.log("error", error);
      handleBackendErrors({ error });
    }
    // Call API with selected permissions (data.permissions)
  };

  const carLogsTypeOptions = useSetSelectOptions({
    data: ProductsTypes?.result,
    getOption: (item) => ({
      label: i18n.language === "eng" ? item?.title?.en : item?.title?.ar,
      value: item.id,
    }),
  });

  const inputsArray = [
    {
      id: 4,
      name: "name",
      type: "text",
      label: t("common.name"),
    },
  ];

  const dataSearch = [
    {
      id: 3,
      label: t("common.group"),
      type: "select",
      name: "product_type_ids",
      options: carLogsTypeOptions,
      isMulti: true,
      cols: 2,
      hasButtonSearch: true,
    },
  ];

  return (
    <div className="page-content">
      <Container fluid style={{ height: "100%" }}>
        <Breadcrumbs
          title={t("products.products")}
          breadcrumbItems={breadcrumbItems}
          addTitle={t("common.add") + " " + t("products.products")}
          handleOrderClicks={handelAddBonds}
          isAddOptions={true}
          canPermission={"product.store"}
        />
        <Card style={{ height: "90%", padding: 20 }}>
          <TableContainer
            hideSHowGFilter={false}
            columns={columns || []}
            data={rowData || []}
            isPagination={true}
            manualPagination={true}
            pageCount={data?.meta?.last_page || 1}
            currentPage={currentPage}
            setPage={setCurrentPage}
            isLoading={isLoading}
            isBordered={true}
            customPageSize={pageSize}
            customHeight={"65vh"}
            customComponent={
              <SearchCard
                SearchData={dataSearch}
                control={control}
                hadelReset={handleReset}
                inputsArray={inputsArray}
                register={register}
                handelSearch={handleSearch}
                watch={watch}
                setValue={setValue}
              />
            }
          />
        </Card>
        <DeleteModal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          onDelete={DeleteFun}
          itemName={t("products.products")}
          isDeleting={isDeleting}
        />
        {openStatsusModal && selectId && (
          <Modal isOpen={openStatsusModal} backdrop="static">
            <ModalHeader toggle={handelCLoseModal}>
              {t("common.Attention")}
            </ModalHeader>
            <ModalBody>
              <p>{t("common.delete_text")}</p>
              <ModalFooter>
                <Button
                  className="btn-sm"
                  type="button"
                  color="light"
                  onClick={handelCLoseModal}
                >
                  {t("common.no")}
                </Button>
                <Button
                  onClick={() =>
                    statusIsActive ? ActiveUser(selectId) : InActive(selectId)
                  }
                  disabled={isDeleting}
                  type="button"
                  color="primary"
                  className="btn-sm"
                >
                  {isDeleting ? (
                    <ClipLoader color="white" size={15} />
                  ) : (
                    t("common.yes")
                  )}
                </Button>
              </ModalFooter>
            </ModalBody>
          </Modal>
        )}
      </Container>
    </div>
  );
};
export default ProductsList;
