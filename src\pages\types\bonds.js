import { useTranslation, withTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardBody,
  Container,
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useMemo, useState } from "react";
import ClipLoader from "react-spinners/ClipLoader";
import toastr from "toastr";
import { BondsAPis } from "../../apis/bound/api";
import { BondsQueries } from "../../apis/bound/query";
import { Link, useLocation } from "react-router-dom";
import { handleBackendErrors, truncateText } from "../../helpers/api_helper";
import { Can } from "../../components/permissions-way/can";
import ActionBondsType from "./action-bonds-types";
import TypesModel from "../../components/Common/types-model";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

import "./roles.scss";

const BondType = () => {
  const [page, setPage] = useState(1);
  const {
    data: ContractTypes,
    isLoading: isLoadingUsers,
    refetch,
  } = BondsQueries.useGetAllBonds({ limit: 10, page: page });
  const { t, i18n } = useTranslation();
  const { pathname } = useLocation();
  const [isShow, setIsShow] = useState(false);
  const [open, setOpen] = useState(false);

  const handelCloseSideBar = () => {
    setOpen(false);
  };

  const [selectId, setSelectId] = useState(null);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setSelectId(null);
    setIsShow(false);
    setOpen(false);
    refetch();
  };
  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const [isDeleting, setIsDeleting] = useState(false);
  const handelSelectId = (id) => {
    setSelectId(id);
    setOpen(true);
  };

  const handelAddBonds = () => {
    // navigate("/action-bonds-types");
    setOpen(true);
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const breadcrumbItems = [
    {
      title: t("types.bonds.voucher"),
      link: pathname,
    },
    // { title: "list", link: pathname },
  ];

  const columns = useMemo(() => [
    // {
    //   Header: "ID",
    //   accessor: "id",
    //   disableFilters: true,
    //   filterable: false,
    // },
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.title"),
      width: 50,
      accessor: "title",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("bonds.voucher_count"),
      accessor: "bonds_count",
      disableFilters: true,
      filterable: false,
    },
    // {
    //   Header: "Total",
    //   accessor: "total",
    //   disableFilters: true,
    //   filterable: false,
    // },
    // {
    //   Header: "Sign",
    //   accessor: "sign",
    //   disableFilters: true,
    //   filterable: false,
    // },
    // {
    //   Header: "Bound",
    //   accessor: "bond",
    //   disableFilters: true,
    //   filterable: false,
    // },
    {
      Header: t("common.type"),
      accessor: "type",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2">
            <Can permission={"bond_type.update"}>
              <Link
                // to={`/action-bonds-types/?id=${cellProps.id}`}
                className="text-primary"
                onClick={() => {
                  handelSelectId(cellProps.id);
                }}
              >
                {/* <i className="mdi mdi-pencil font-size-18"></i> */}
                {/* <HiPencilAlt /> */}
                <FaPenToSquare size={14} />
              </Link>
            </Can>
            <Can permission={"bond_type.destroy"}>
              {cellProps.is_default !== 1 && (
                <Link
                  onClick={() => {
                    if (cellProps.isDefault !== 1) {
                      handelOpenModal();
                      // handelSelectId(cellProps.id);
                      setSelectId(cellProps.id);
                    }
                  }}
                  to="#"
                  className="text-danger"
                >
                  {/* <i className="mdi mdi-trash-can font-size-18"></i> */}
                  <MdDeleteSweep size={18} />
                </Link>
              )}
            </Can>
            <Can permission={"bond_type.show"}>
              <Link
                // to={`/action-bonds-types/?id=${cellProps.id}?Show=true`}
                className="text-success"
                onClick={() => {
                  handelSelectId(cellProps.id);
                  setIsShow(true);
                }}
              >
                <FaInfoCircle size={14} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      ContractTypes?.result?.length > 0
        ? ContractTypes.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: (page - 1) * 10 + index + 1, // 10 is your page size
              title:
                i18n.language === "eng"
                  ? truncateText({
                      text:
                        i18n.language === "eng" ? item.title.en : item.title.ar,
                      maxLengthPercent: 0.3,
                    })
                  : truncateText({
                      text:
                        i18n.language === "eng" ? item.title.en : item.title.ar,
                      maxLengthPercent: 0.3,
                    }),
              description:
                truncateText({
                  text: item.description,
                  maxLengthPercent: 0.3,
                }) || "----",
              bonds_count: item.bonds_count,
              total: item.total === 0 ? "false" : "true",
              sign: item.sign === 0 ? "false" : "true",
              bond: item.bond === 0 ? "false" : "true",
              type:
                item.payment_type === 1
                  ? t("common.catch")
                  : t("common.payment"),
              is_default: item.is_default,
              is_setting: item.is_setting,
            }))
            .reverse()
        : [],
    [ContractTypes?.result, t, refetch]
  );

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await BondsAPis.deleteBonds({
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
      // toastr.error(error?.response?.data?.message);
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("types.bonds.voucher")}
          breadcrumbItems={breadcrumbItems}
          addTitle={t("common.add") + " " + t("types.bonds.voucher")}
          isAddOptions={true}
          handleOrderClicks={handelAddBonds}
          canPermission="bond_type.store"
        />
        <Card style={{ height: "78vh" }}>
          <CardBody>
            <TableContainer
              hideSHowGFilter={false}
              columns={columns || []}
              data={rowData || []}
              isPagination={true}
              iscustomPageSize={true}
              isBordered={true}
              pageSize={10}
              pageIndex={page}
              manualPagination={true}
              pageCount={ContractTypes?.meta?.last_page || 1}
              currentPage={page}
              setPage={setPage}
              addTitle={t("common.add") + " " + t("types.bonds.voucher")}
              isAddOptions
              canPermission={"bond_type.store"}
              handleOrderClicks={handelAddBonds}
              isLoading={isLoadingUsers}
            />
            {/* )} */}
          </CardBody>
        </Card>
      </Container>

      <TypesModel
        open={open}
        handelClose={handelCloseSideBar}
        hideAll={true}
        content={
          <div>
            <h1 style={{ fontSize: 16 }} className="mb-4">
              {isShow
                ? t("common.show") + " " + t("types.bonds.voucher")
                : selectId
                ? t("common.update") + " " + t("types.bonds.voucher")
                : t("common.add") + " " + t("types.bonds.voucher")}
            </h1>
            <ActionBondsType
              handelClose={handelCLoseModal}
              isShow={isShow}
              selectedId={selectId}
            />
          </div>
        }
      />
      <Modal
        isOpen={openDeleteMdal}
        toggle={handelCLoseModal}
        backdrop="static"
      >
        <ModalHeader toggle={handelCLoseModal}>
          {t("common.delete") + " " + t("types.bonds.voucher")}
        </ModalHeader>
        <ModalBody>
          <p>{t("common.delete_text")}</p>
          <ModalFooter>
            <Button type="button" color="light" onClick={handelCLoseModal}>
              {t("common.close")}
            </Button>
            <Button
              disabled={isDeleting}
              onClick={DeleteFun}
              type="button"
              color="danger"
            >
              {isDeleting ? (
                <ClipLoader color="white" size={15} />
              ) : (
                t("common.delete")
              )}
            </Button>
          </ModalFooter>
        </ModalBody>
      </Modal>
    </div>
  );
};
export default withTranslation()(BondType);
