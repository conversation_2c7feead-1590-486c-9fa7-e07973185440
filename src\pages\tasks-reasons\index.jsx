import ClipLoader from "react-spinners/ClipLoader";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Container,
  Input,
  Modal,
  ModalB<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Mo<PERSON>Header,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import { useMemo, useState } from "react";
import { reasonsTypesQueries } from "../../apis/types/resons/query";
import toastr from "toastr";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { reasonsTypesAPis } from "../../apis/types/resons/api";
import { useTranslation } from "react-i18next";
import { Link, useLocation } from "react-router-dom";
import {
  AdmminAprovapleTranslation,
  ReasonsEnum,
} from "../../constant/constants";
import {
  handleBackendErrors,
  hasPermission,
  truncateText,
} from "../../helpers/api_helper";
import ActionReasons from "../types/action-reasons";
import { Can } from "../../components/permissions-way/can";
// import ApproveList from "../approve-list/index";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

const TasksTypes = () => {
  const { t, i18n } = useTranslation();
  const [page, setPage] = useState(1);
  const {
    data: ContractTypes,
    isLoading: isLoadingTasksTypes,
    refetch,
  } = reasonsTypesQueries.useGetAll({ limit: 10, page: page });

  const [isApproveList, setIsApproveList] = useState(0);

  const handelSelect = (id) => {
    setIsApproveList(id);
  };
  const { pathname } = useLocation();
  const [selectId, setSelectId] = useState(null);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [isShow, setIsShow] = useState(false);
  const [openStatsusModal, setOpenStatsusModal] = useState(false);
  const [statusIsActive, setstausIsActive] = useState(false);
  const [openAddModal, setOpeAddModal] = useState(false);

  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setSelectId(null);
    setstausIsActive(false);
    setOpenStatsusModal(false);
    setIsShow(false);
    setOpeAddModal(false);
    refetch();
  };
  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const [isDeleting, setIsDeleting] = useState(false);
  const handelSelectId = (id) => {
    setSelectId(id);
  };

  const handelAddBonds = () => {
    // navigate("/action-reasons");
    setOpeAddModal(true);
  };

  const ActiveUser = async (id) => {
    try {
      setIsDeleting(true);
      const response = await reasonsTypesAPis.active({
        id: id,
      });
      refetch();
      toastr.success(response.message);
      handelCLoseModal();
      setIsDeleting(false);
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      // console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };
  const InActive = async (id) => {
    try {
      setIsDeleting(true);
      await reasonsTypesAPis.inActive({
        id: id,
      });
      refetch();
      handelCLoseModal();
      setIsDeleting(false);
      toastr.success("in Active done");
    } catch (error) {
      setIsDeleting(false);
      toastr.error("There are error");
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  const hasPErmission = hasPermission("reason.approve_reason");
  const indexhasPErmission = hasPermission("reason.reject_reason");

  const reasonsLis = [
    { id: 0, title: t("types.reason.reasons_list"), show: indexhasPErmission },
    {
      id: 1,
      title: t("types.reason.approval_reasons_list"),
      show: hasPErmission,
    },
  ];

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const breadcrumbItems = [
    {
      title: t("types.reason.reasons"),
      link: pathname,
    },
    // { title: "list", link: pathname },
  ];

  const columns = [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.title"),
      width: 50,
      accessor: "title",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.type"),
      accessor: "type",
      disableFilters: true,
      filterable: false,
    },

    {
      Header: t("common.color"),
      accessor: (cellProps) => (
        <div
          style={{
            background: cellProps.color,
            width: "60%",
            height: 30,
            borderRadius: 20,
          }}
        />
      ),
      disableFilters: true,
      filterable: false,
    },

    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2">
            <Can permission={"reason.update"}>
              <Link
                className="text-primary"
                onClick={() => {
                  handelSelectId(cellProps.id);
                  setOpeAddModal(true);
                }}
              >
                <FaPenToSquare size={14} />
              </Link>
            </Can>
            <Can permission={"reason.destroy"}>
              {!cellProps.is_default && (
                <Link
                  onClick={() => {
                    if (!Boolean(cellProps.is_default)) {
                      handelOpenModal();

                      setSelectId(cellProps.id);
                    }
                  }}
                  to="#"
                  className="text-danger"
                >
                  <MdDeleteSweep size={18} />
                </Link>
              )}
            </Can>
            <Can permission={"reason.show"}>
              <Link
                className="text-success"
                onClick={() => {
                  handelSelectId(cellProps.id);
                  setOpeAddModal(true);
                  setIsShow(true);
                }}
              >
                <FaInfoCircle size={14} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ];

  function getKeyByValue2(value) {
    const x = Object.keys(ReasonsEnum).find(
      (key) => ReasonsEnum[key] === value
    );
    return t(`types.reason.${x.toLocaleLowerCase()}`);
  }

  const rowData = useMemo(
    () =>
      ContractTypes?.result?.length > 0
        ? ContractTypes.result
            .filter((item) => item.is_default) // ← فقط العناصر التي is_default = true
            .map((item, index) => ({
              id: item.id,
              id_toShow: (page - 1) * 10 + index + 1, // 10 هو حجم الصفحة
              title:
                i18n.language === "eng"
                  ? item.title.en
                  : item.title.ar || "----",
              is_default: item.is_default,
              is_default_toShow: item.is_default
                ? t("common.true")
                : t("common.false"),
              admin_approval:
                AdmminAprovapleTranslation(t)[item.admin_approval] || "----",
              type: getKeyByValue2(item.type) || 0,
              status: item.status,
              color: item?.color,
            }))
            .reverse()
        : [],
    [ContractTypes?.result, t, i18n.language, page]
  );

  const {
    data: apprve,
    isLoading,
    refetch: refetechApprove,
  } = reasonsTypesQueries.useGetAllApproveList({
    limit: 100,
    page: 1,
    enabled: isApproveList === 1,
  });

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await reasonsTypesAPis.deleteFu({
        id: selectId,
      });
      refetch();
      refetechApprove();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
    }
    // Call API with selected permissions (data.permissions)
  };

  const approvecolumns = [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.title"),
      width: 50,
      accessor: "title",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.type"),
      accessor: "type",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.color"),
      accessor: (cellProps) => (
        <div style={{ background: cellProps.color, width: 80, height: 40 }} />
      ),
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2">
            <Can permission={"reason.destroy"}>
              <Link
                onClick={() => {
                  if (cellProps.isDefault !== 1) {
                    handelOpenModal();
                    handelSelectId(cellProps.id);
                  }
                }}
                to="#"
                className="text-danger"
              >
                <i className="mdi mdi-trash-can font-size-18"></i>
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ];

  const AprroverowData = useMemo(
    () =>
      apprve?.result?.length > 0
        ? apprve.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: index + 1,
              title:
                i18n.language === "eng"
                  ? truncateText({ text: item.title.en, maxLengthPercent: 0.1 })
                  : truncateText({
                      text: item.title.ar,
                      maxLengthPercent: 0.3,
                    }) || "----",
              is_default: item.is_default,
              is_default_toShow: item.is_default
                ? t("common.true")
                : t("common.false"),
              admin_approval:
                AdmminAprovapleTranslation(t)[item.admin_approval] || "----",
              type: getKeyByValue2(item.type) || 0,
            }))
            .reverse()
        : [],
    [apprve?.result]
  );

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("types.reason.task_reason")}
          breadcrumbItems={breadcrumbItems}
          isAddOptions={false}
          // addTitle={t("common.add") + " " + t("types.reason.reasons")}
          // handleOrderClicks={handelAddBonds}
          canPermission={"reason.store"}
        />
        <Card style={{ height: "80vh", padding: 20 }}>
          {isApproveList === 1 ? (
            <TableContainer
              hideSHowGFilter={false}
              columns={approvecolumns || []}
              data={AprroverowData || []}
              pageSize={10}
              pageIndex={page}
              isLoading={isLoadingTasksTypes || isLoading}
              manualPagination={true}
              pageCount={ContractTypes?.meta?.last_page || 1}
              currentPage={page}
              setPage={setPage}
              hidePagination
              className="custom-header-css table align-middle table-nowrap"
              tableClassName="table-centered align-middle table-nowrap mb-0"
              theadClassName="text-muted table-light"
            />
          ) : (
            <TableContainer
              hideSHowGFilter={false}
              columns={columns || []}
              data={rowData || []}
              canPermission={"reason.store"}
              addTitle={t("common.add") + " " + t("types.reason.reasons")}
              isLoading={isLoadingTasksTypes || isLoading}
              pageSize={10}
              pageIndex={page}
              pageCount={ContractTypes?.meta?.last_page || 1}
              currentPage={page}
              customHeight={"65%"}
              setPage={setPage}
              className="custom-header-css table align-middle table-nowrap"
              tableClassName="table-centered align-middle table-nowrap mb-0"
              theadClassName="text-muted table-light"
            />
          )}
        </Card>
        <Modal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          backdrop="static"
        >
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.delete")} {t("types.reason.reasons")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button type="button" color="light" onClick={handelCLoseModal}>
                {t("common.close")}
              </Button>
              <Button onClick={DeleteFun} type="button" color="danger">
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.delete")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>

        <Modal
          isOpen={openAddModal}
          toggle={handelCLoseModal}
          backdrop="static"
          fade={false}
        >
          <ModalHeader toggle={handelCLoseModal}>
            {isShow
              ? t("types.reason.reason")
              : selectId
              ? t("common.update") + " " + t("types.reason.reason")
              : t("common.add") + " " + t("types.reason.reason")}
          </ModalHeader>
          <ModalBody>
            <ActionReasons
              isShow={isShow}
              selectId={selectId}
              handelCLoseModal={handelCLoseModal}
              refetch={refetch}
              has
            />
          </ModalBody>
        </Modal>
      </Container>
      {openStatsusModal && selectId && (
        <Modal isOpen={openStatsusModal} backdrop="static">
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.Attention")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button type="button" color="light" onClick={handelCLoseModal}>
                {t("common.no")}
              </Button>
              <Button
                onClick={() =>
                  statusIsActive ? ActiveUser(selectId) : InActive(selectId)
                }
                disabled={isDeleting}
                type="button"
                color="primary"
              >
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.yes")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
      )}
    </div>
  );
};
export default TasksTypes;
