import React, { useState, useEffect } from "react";
import Select from "react-select";
import CreatableSelect from "react-select/creatable";
import { Controller } from "react-hook-form";

const CustomSelect = ({
  name,
  control,
  options,
  label,
  isMulti = false,
  isClearable = true,
  isDisabled = false,
  defaultValue = null,
  handleSearch,
  className = "",
  menuHeight = 100,
  error,
  isLoading = false,
  placeholder,
  onFocusChange,
  creatable = false,
  customOnChange,
  formatCreateLabel,
  menuPosition,
  ...rest
}) => {
  const [isFocus, setIsFocus] = useState(false);
  const [hasValue, setHasValue] = useState(false);

  const SelectComponent = creatable ? CreatableSelect : Select;

  useEffect(() => {
    if (defaultValue) {
      setHasValue(true);
    }
  }, [defaultValue]);

  const handleFocus = () => {
    setIsFocus(true);
    if (onFocusChange) onFocusChange(true, name);
  };

  const handleBlur = () => {
    setIsFocus(false);
    if (onFocusChange) onFocusChange(false, name);
  };

  return (
    <div className={className}>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        render={({ field }) => {
          if (field.value && !hasValue) {
            setHasValue(true);
          }

          return (
            <div
              className={`position-relative ${isFocus ? "focused" : ""}`}
              style={{ transition: "all 0.5s ease-in-out" }}
            >
              {/* Floating Label */}
              <div
                className="position-absolute floating-label"
                style={{
                  top: isFocus || hasValue ? -10 : 11,
                  insetInlineStart: isFocus || hasValue ? 5 : 10,
                  fontSize: isFocus || hasValue ? 12 : 15,
                  background: "white",
                  display: "flex",
                  alignItems: "center",
                  width: "auto",
                  height: "fit-content",
                  padding: "0 5px",
                  color: isFocus ? "#0d6efd" : "#808080",
                  transition: "all 0.3s ease-in-out",
                  zIndex: 10,
                  pointerEvents: "none",
                }}
              >
                <span>{placeholder || label}</span>
              </div>

              <SelectComponent
                {...field}
                {...rest}
                isMulti={isMulti}
                isClearable={isClearable}
                isDisabled={isDisabled || isLoading}
                options={options}
                placeholder=""
                value={field.value}
                formatCreateLabel={formatCreateLabel}
                onChange={(selectedOption) => {
                  if (customOnChange) {
                    customOnChange(selectedOption, field.onChange);
                  } else {
                    field.onChange(selectedOption);
                    setHasValue(!!selectedOption);
                    if (handleSearch) setTimeout(() => handleSearch());
                  }
                }}
                menuPosition={menuPosition}
                onFocus={handleFocus}
                hideSelectedOptions
                onBlur={handleBlur}
                styles={{
                  control: (props, state) => ({
                    ...props,
                    borderRadius: "0.375rem",
                    minHeight: "37px",
                    fontSize: "0.8rem",
                    border: error?.message
                      ? "2px solid #f00"
                      : state.isFocused
                      ? "2px solid #0d6efd"
                      : "2px solid #ccd",
                    boxShadow: state.isFocused
                      ? "0 0 0 0.2rem rgba(13,110,253,.25)"
                      : "none",
                    transition: "all 0.3s ease-in-out",
                    "&:hover": {
                      borderColor: state.isFocused ? "#0d6efd" : "#aab",
                    },
                    padding: "0 8px",
                  }),
                  placeholder: (provided) => ({
                    ...provided,
                    color: "#fff",
                  }),
                  valueContainer: (provided) => ({
                    ...provided,
                    padding: "2px 8px 2px 8px",
                    marginTop: hasValue || isFocus ? "8px" : "0",
                  }),
                  menuList: (props) => ({
                    ...props,
                    height: `${menuHeight}px`,
                  }),
                  menu: (props) => ({
                    ...props,
                    height: `${menuHeight}px`,
                    zIndex: 20,
                  }),
                  indicatorSeparator: (provided) => ({
                    ...provided,
                    display: "none",
                  }),
                  indicatorContainer: (provided) => ({
                    ...provided,
                    display: "none !important",
                  }),
                  singleValue: (provided) => ({
                    ...provided,
                    color: "#000",
                    fontSize: "0.7rem",
                  }),
                  clearIndicator: (provided) => ({
                    ...provided,
                    display: "none",
                  }),
                }}
              />

              {isLoading && (
                <div
                  className="position-absolute end-0 top-50 translate-middle-y me-2"
                  style={{ zIndex: 15 }}
                >
                  <div
                    className="spinner-border spinner-border-sm text-primary"
                    role="status"
                    style={{
                      width: "1rem",
                      height: "1rem",
                      borderWidth: "0.15em",
                    }}
                  >
                    <span className="visually-hidden">Loading...</span>
                  </div>
                </div>
              )}
            </div>
          );
        }}
      />
      {error && <div className="invalid-feedback d-block">{error.message}</div>}
    </div>
  );
};

export default CustomSelect;
