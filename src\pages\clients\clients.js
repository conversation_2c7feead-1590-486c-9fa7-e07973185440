import ClipLoader from "react-spinners/ClipLoader";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Container,
  Input,
  Modal,
  ModalBody,
  <PERSON><PERSON><PERSON><PERSON>er,
  ModalHeader,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import { useMemo, useState, useEffect } from "react";
import { clientsQueries } from "../../apis/clients/query";
import toastr from "toastr";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { clientsAPis } from "../../apis/clients/api";
import {
  handleBackendErrors,
  hasPermission,
  truncateText,
} from "../../helpers/api_helper";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Can } from "../../components/permissions-way/can";
import useSetSelectOptions from "../../hooks/use-set-select-options";
import { clientsGroupsQueries } from "../../apis/client-group/query";
import { useForm } from "react-hook-form";
import SearchCard from "../../components/Reports/search-card";
import CustomFilterSearch from "../../components/Common/CustomFilterSearch";
import { statesQueries } from "../../apis/states/query";
import { citiesQueries } from "../../apis/cities/query";
import { locationQueries } from "../../apis/locations/query";
import { delegateQueries } from "../../apis/delegate/query";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";
import { LiaSitemapSolid } from "react-icons/lia";

// import CLientProduct from "./client-product";
// import { Can } from "../../components/permissions-way/can";

const TasksTypes = () => {
  const [searchParams, setSearchParams] = useState({});
  const { pathname, search } = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [selectId, setSelectId] = useState(null);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [openStatsusModal, setOpenStatsusModal] = useState(false);
  const [statusIsActive, setstausIsActive] = useState(false);
  const [openAddModal, setOpenAddModal] = useState(false);
  const [page, setPage] = useState(1);
  const queryParams = useMemo(() => new URLSearchParams(search), [search]);

  // Selected IDs for dependent filters
  const [selectedStateIds, setSelectedStateIds] = useState([]);
  const [selectedCityIds, setSelectedCityIds] = useState([]);

  const { data, isLoading, refetch } = clientsQueries.useGetAll({
    limit: 6,
    page: page,
    searchParams,
  });

  // Fetch filter data
  const { data: client_group } = clientsGroupsQueries.useGetAll({ status: 1 });
  const { data: statesData } = statesQueries.useGetAll({ status: 1 });
  const { data: delegatesData } = delegateQueries.useGetAll({ status: 1 });

  // Fetch dependent data with the appropriate query functions
  const { data: filteredCitiesData } = citiesQueries.useGetSearchCityStates({
    state_ids: selectedStateIds,
  });

  const { data: filteredLocationsData } =
    locationQueries.useGetSearchLocationsByCities({
      city_ids: selectedCityIds,
    });

  const { control, watch, register, setValue, reset } = useForm({
    defaultValues: {
      name: "",
      client_group_ids: null,
      state_ids: null,
      city_ids: null,
      location_ids: null,
      delegate_ids: null,
      status: null,
      no_group: null,
    },
  });

  // Watch all search fields
  const searchFields = watch([
    "name",
    "client_group_ids",
    "",
    "state_ids",
    "city_ids",
    "location_ids",
    "delegate_ids",
    "status",
    "no_group",
  ]);

  // Handle state selection to filter cities
  useEffect(() => {
    if (searchFields[3]) {
      // When state_ids changes
      if (Array.isArray(searchFields[3])) {
        setSelectedStateIds(searchFields[3].map((item) => item.value));
      } else if (searchFields[3]?.value) {
        setSelectedStateIds([searchFields[3].value]);
      } else {
        setSelectedStateIds([]);
      }

      // Reset dependent fields when parent changes
      // setValue("city_ids", null);
      // setValue("location_ids", null);
    } else {
      setSelectedStateIds([]);
    }
    // Don't trigger search automatically
  }, [searchFields[3]]);

  // Handle city selection to filter locations
  useEffect(() => {
    if (searchFields[4]) {
      // When city_ids changes
      if (Array.isArray(searchFields[4])) {
        setSelectedCityIds(searchFields[4].map((item) => item.value));
      }
      // console.log("click", searchFields[4]);
      // Reset dependent fields when parent changes
      // setValue("location_ids", null);
    } else {
      setSelectedCityIds([]);
    }
    // Don't trigger search automatically
  }, [searchFields[4]]);

  // Example for state_ids:
  useEffect(() => {
    if (!searchFields[3] || searchFields[3].length === 0) {
      setValue("city_ids", null);
      setValue("location_ids", null);
    }
  }, [searchFields[3]]);

  // Add manual search function
  const handleManualSearch = () => {
    const params = {};

    console.log("dsadsd", searchFields);

    // Company name search
    if (searchFields[0]) {
      params["filter[name]"] = searchFields[0];
    }

    // Client group filter
    if (searchFields[1]) {
      // Handle both single object and array of objects
      if (Array.isArray(searchFields[1])) {
        if (searchFields[1].length > 0) {
          // Create separate parameters for each client group ID
          searchFields[1].forEach((group) => {
            if (!params["filter[client_group_ids][]"]) {
              params["filter[client_group_ids][]"] = [group.value];
            } else {
              params["filter[client_group_ids][]"].push(group.value);
            }
          });
        }
      } else if (searchFields[2].value) {
        // Single object case
        params["filter[client_group_ids][]"] = [searchFields[1].value];
      }
    }

    // States filter
    if (searchFields[3]) {
      if (Array.isArray(searchFields[3])) {
        if (searchFields[3].length > 0) {
          searchFields[3].forEach((state) => {
            if (!params["filter[state_ids][]"]) {
              params["filter[state_ids][]"] = [state.value];
            } else {
              params["filter[state_ids][]"].push(state.value);
            }
          });
        }
      } else if (searchFields[3].value) {
        params["filter[state_ids][]"] = [searchFields[3].value];
      }
    }

    // Cities filter
    if (searchFields[4]) {
      if (Array.isArray(searchFields[4])) {
        if (searchFields[4].length > 0) {
          searchFields[4].forEach((city) => {
            if (!params["filter[city_ids][]"]) {
              params["filter[city_ids][]"] = [city.value];
            } else {
              params["filter[city_ids][]"].push(city.value);
            }
          });
        }
      } else if (searchFields[4].value) {
        params["filter[city_ids][]"] = [searchFields[4].value];
      }
    }

    // Locations filter
    if (searchFields[5]) {
      if (Array.isArray(searchFields[5])) {
        if (searchFields[5].length > 0) {
          searchFields[5].forEach((location) => {
            if (!params["filter[location_ids][]"]) {
              params["filter[location_ids][]"] = [location.value];
            } else {
              params["filter[location_ids][]"].push(location.value);
            }
          });
        }
      } else if (searchFields[5].value) {
        params["filter[location_ids][]"] = [searchFields[5].value];
      }
    }

    // Delegates filter
    if (searchFields[6]) {
      if (Array.isArray(searchFields[6])) {
        if (searchFields[6].length > 0) {
          searchFields[6].forEach((delegate) => {
            if (!params["filter[delegate_ids][]"]) {
              params["filter[delegate_ids][]"] = [delegate.value];
            } else {
              params["filter[delegate_ids][]"].push(delegate.value);
            }
          });
        }
      } else if (searchFields[6].value) {
        params["filter[delegate_ids][]"] = [searchFields[6].value];
      }
    }

    // Status filter (active/inactive)
    if (searchFields[7] && searchFields[7].value !== null) {
      params["filter[status]"] = searchFields[7].value;
    }

    // No group filter (with group/without group)
    if (searchFields[8] && searchFields[8].value !== null) {
      params["filter[no_group]"] = searchFields[8].value;
    }

    // Update search params
    setSearchParams(params);
    // Update URL with new filters
    updateUrlWithFilters(params);
    // Reset to first page when filters change
    setPage(1);
    refetch();
  };

  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setSelectId(null);
    setstausIsActive(false);
    setOpenStatsusModal(false);
  };
  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const [isDeleting, setIsDeleting] = useState(false);
  const handelSelectId = (id) => {
    setSelectId(id);
  };

  const handelAddBonds = () => {
    navigate("/action-clients");
  };

  const ActiveUser = async (id) => {
    try {
      setIsDeleting(true);
      const response = await clientsAPis.active({
        id: id,
      });
      refetch();
      toastr.success(response.message);
      handelCLoseModal();
      setIsDeleting(false);
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };
  const InActive = async (id) => {
    try {
      setIsDeleting(true);
      const response = await clientsAPis.inActive({
        id: id,
      });
      refetch();
      handelCLoseModal();
      setIsDeleting(false);
      // toastr.success("in Active done");
      toastr.success(response.message);
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const breadcrumbItems = [
    {
      title: t("clients.clients"),
      link: pathname,
    },
  ];

  const handelToggleStatus = ({ cellProps }) => {
    if (cellProps.status === 1 && hasPermission("client.disactivate")) {
      setSelectId(cellProps.id);
      setOpenStatsusModal(true);
      setstausIsActive(false);
      // setIsChecked(true);
    } else if (hasPermission("client.activate")) {
      setSelectId(cellProps.id);
      setOpenStatsusModal(true);
      setstausIsActive(true);
      // setIsChecked(false);
    }
  };
  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: (cellProps) => (
        <div>
          <p
            style={{
              background:
                cellProps.warranty_status === 2
                  ? "#ebd64a"
                  : cellProps.warranty_status === 3
                  ? "#51dd13"
                  : "#dd1313",
              width: "20px",
              height: "20px",
              color: "#fff",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              borderRadius: "50%",
            }}
          >
            {cellProps.id_toShow}
          </p>
        </div>
      ),
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.full_name"),
      accessor: "full_name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("clients.company_name"),
      accessor: "company_name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.email"),
      accessor: "email",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.delegate"),
      accessor: "delegate",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("clients.client_group"),
      accessor: "client_group",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("clients.address"),
      accessor: "address",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.status"),
      disableFilters: true,
      filterable: false,
      accessor: (cellProps) => {
        return (
          // <Can permission={"client.activate"}>
          <div className="form-check form-switch">
            <Input
              type="checkbox"
              className="form-check-input"
              // defaultChecked={isChecked}
              checked={cellProps.status === 1}
              onClick={() => handelToggleStatus({ cellProps })}
              // onClick={() => handelToggleStatus({ cellProps })}
            />
          </div>
          // </Can>
        );
      },
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2 justify-content-center">
            <Can permission={"client.update"}>
              <Link
                to={
                  cellProps.is_default !== 1 &&
                  `/action-clients?id=${cellProps.id}`
                }
                className="text-primary"
                onClick={() => {}}
              >
                {/* <i className="mdi mdi-pencil font-size-16"></i> */}
                <FaPenToSquare size={14} />
              </Link>
            </Can>
            <Can permission={"client.destroy"}>
              <Link
                onClick={() => {
                  if (cellProps.isDefault !== 1) {
                    handelOpenModal();
                    handelSelectId(cellProps.id);
                  }
                }}
                to="#"
                className="text-danger"
              >
                {/* <i className="mdi mdi-trash-can font-size-16"></i> */}
                <MdDeleteSweep size={18} />
              </Link>
            </Can>
            <Can permission={"client.product"}>
              <Link
                to={`/client-product?id=${cellProps.id}?warranty_status=${cellProps.warranty_status}`}
                className="text-info"
              >
                {/* <i className="ri-product-hunt-line font-size-16"></i> */}
                <LiaSitemapSolid size={18} />
              </Link>
            </Can>
            <Can permission={"client.show"}>
              <Link
                to={`/action-clients/?id=${cellProps.id}?Show=true`}
                className="text-success"
              >
                {/* <i className=" ri-information-fill font-size-16"></i> */}
                <FaInfoCircle size={14} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      data?.result?.length > 0
        ? data.result
            .map((item, index) => ({
              id: item.id, // Incremental ID starting from 1
              id_toShow: (page - 1) * 10 + index + 1, // 10 is your page size
              full_name:
                truncateText({
                  text: item.full_name,
                  maxLengthPercent: 0.3,
                }) || "----",
              company_name:
                truncateText({
                  text: item.company_name,
                  maxLengthPercent: 0.3,
                }) || "----",
              email:
                truncateText({
                  text: item.email,
                  maxLengthPercent: 0.3,
                }) || "----",
              // image: item.image,
              delegate:
                truncateText({
                  text: item.delegate?.full_name,
                  maxLengthPercent: 0.3,
                }) || "----",
              status: item.status,
              originalId: item.id, // Keep the original ID if needed
              client_group: item?.client_group?.group_title || "----", // Keep the original ID if needed
              address:
                truncateText({
                  text: item.address,
                  maxLengthPercent: 0.3,
                }) || "---",
              warranty_status: item?.warranty_status,
            }))
            .reverse()
        : [],
    [data?.result]
  );

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await clientsAPis.deleteFu({
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  const clientGroupsOptions = useSetSelectOptions({
    data: client_group?.result,
    getOption: (item) => ({ label: item.group_title, value: item.id }),
  });

  const stateOptions = useSetSelectOptions({
    data: statesData?.result,
    getOption: (item) => ({
      label: item.name,
      value: item.id,
    }),
  });

  const delegateOptions = useSetSelectOptions({
    data: delegatesData?.result,
    getOption: (item) => ({ label: item.full_name, value: item.id }),
  });

  // Use filtered cities data from API response
  const cityOptions = useSetSelectOptions({
    data: filteredCitiesData?.result || [],
    getOption: (item) => ({
      label: item.name,
      value: item.id,
    }),
  });

  // Use filtered locations data from API response
  const locationOptions = useSetSelectOptions({
    data: filteredLocationsData?.result || [],
    getOption: (item) => ({
      label: item.name,
      value: item.id,
    }),
  });

  const statusOptions = [
    {
      label: t("common.active"),
      value: 1,
    },
    {
      label: t("common.in_active"),
      value: 2,
    },
  ];

  const noGroupOptions = [
    {
      label: t("clients.all"),
      value: 0,
    },
    {
      label: t("clients.without_group"),
      value: 2,
    },
    {
      label: t("clients.with_group"),
      value: 1,
    },
  ];

  const handleReset = () => {
    // Reset form fields
    reset({
      name: "",
      client_group_ids: null,
      state_ids: null,
      city_ids: null,
      location_ids: null,
      delegate_ids: null,
      status: null,
      no_group: null,
    });

    // Reset dependent filter states
    setSelectedStateIds([]);
    setSelectedCityIds([]);

    // Clear search params
    setSearchParams({});
    setPage(1);

    // Clear URL params
    navigate(pathname, { replace: true });

    // Refetch data with empty params
    refetch();
  };

  // Filter fields configuration for SearchCard
  const SearchData = [
    {
      id: 1,
      label: t("clients.client_group"),
      type: "select",
      name: "client_group_ids",
      options: clientGroupsOptions,
      isMulti: true,
      cols: 2,
      component: CustomFilterSearch,
      hasButtonSearch: true,
    },
    {
      id: 2,
      label: t("common.state"),
      type: "select",
      name: "state_ids",
      options: stateOptions,
      isMulti: true,
      cols: 2,
      component: CustomFilterSearch,
      hasButtonSearch: true,
    },
    {
      id: 3,
      label: t("common.city"),
      type: "select",
      name: "city_ids",
      options: cityOptions,
      isMulti: true,
      cols: 2,
      component: CustomFilterSearch,
      isDisabled: selectedStateIds.length === 0,
      hasButtonSearch: true,
    },
    {
      id: 4,
      label: t("common.location"),
      type: "select",
      name: "location_ids",
      options: locationOptions,
      isMulti: true,
      cols: 2,
      component: CustomFilterSearch,
      isDisabled: selectedCityIds.length === 0,
      hasButtonSearch: true,
    },
    {
      id: 5,
      label: t("common.delegate"),
      type: "select",
      name: "delegate_ids",
      options: delegateOptions,
      isMulti: true,
      cols: 2,
      component: CustomFilterSearch,
      hasButtonSearch: true,
    },
    {
      id: 6,
      label: t("common.status"),
      type: "select",
      name: "status",
      options: statusOptions,
      isMulti: false,
      cols: 2,
      component: CustomFilterSearch,
      hasButtonSearch: true,
    },
    {
      id: 7,
      label: t("common.client_status"),
      type: "select",
      name: "no_group",
      options: noGroupOptions,
      isMulti: false,
      cols: 2,
      component: CustomFilterSearch,
      hasButtonSearch: true,
    },
  ];

  // Input fields configuration for SearchCard
  const inputsArray = [
    {
      id: 9,
      name: "name",
      type: "text",
      label: t("common.name"),
      cols: 2,
    },
  ];

  // Add function to update URL with search parameters
  const updateUrlWithFilters = (params) => {
    const newUrl = new URLSearchParams();

    // Client group filter
    if (params["filter[client_group_ids][]"]) {
      const groupIds = Array.isArray(params["filter[client_group_ids][]"])
        ? params["filter[client_group_ids][]"]
        : [params["filter[client_group_ids][]"]];

      groupIds.forEach((id) => newUrl.append("client_group_ids[]", id));
    }

    // States filter
    if (params["filter[state_ids][]"]) {
      const stateIds = Array.isArray(params["filter[state_ids][]"])
        ? params["filter[state_ids][]"]
        : [params["filter[state_ids][]"]];

      stateIds.forEach((id) => newUrl.append("state_ids[]", id));
    }

    // Cities filter
    if (params["filter[city_ids][]"]) {
      const cityIds = Array.isArray(params["filter[city_ids][]"])
        ? params["filter[city_ids][]"]
        : [params["filter[city_ids][]"]];

      cityIds.forEach((id) => newUrl.append("city_ids[]", id));
    }

    // Locations filter
    if (params["filter[location_ids][]"]) {
      const locationIds = Array.isArray(params["filter[location_ids][]"])
        ? params["filter[location_ids][]"]
        : [params["filter[location_ids][]"]];

      locationIds.forEach((id) => newUrl.append("location_ids[]", id));
    }

    // Delegates filter
    if (params["filter[delegate_ids][]"]) {
      const delegateIds = Array.isArray(params["filter[delegate_ids][]"])
        ? params["filter[delegate_ids][]"]
        : [params["filter[delegate_ids][]"]];

      delegateIds.forEach((id) => newUrl.append("delegate_ids[]", id));
    }

    if (params["filter[name]"]) {
      newUrl.set("name", params["filter[name]"]);
    }

    // Status filter
    if (params["filter[status]"] !== undefined) {
      newUrl.set("status", params["filter[status]"]);
    }

    // No group filter
    if (params["filter[no_group]"] !== undefined) {
      newUrl.set("no_group", params["filter[no_group]"]);
    }

    // Replace current URL without reloading the page
    navigate(`${pathname}?${newUrl.toString()}`, { replace: true });
  };

  // Add function to parse URL params when page loads
  const parseUrlParams = () => {
    try {
      const params = {};
      const initialFormValues = {
        name: "",
        client_group_ids: null,
        state_ids: null,
        city_ids: null,
        location_ids: null,
        delegate_ids: null,
        status: null,
        no_group: null,
      };

      // Parse text search fields
      // const companyName = queryParams.get("company_name");
      // if (companyName) {
      //   params["filter[company_name]"] = companyName;
      //   initialFormValues.company_name = companyName;
      // }

      const fullName = queryParams.get("name");
      if (fullName) {
        params["filter[name]"] = fullName;
        initialFormValues.name = fullName;
      }

      // Parse client group IDs
      const clientGroupIds = queryParams.getAll("client_group_ids[]");
      if (clientGroupIds.length > 0 && clientGroupsOptions.length > 0) {
        params["filter[client_group_ids][]"] = clientGroupIds;
        initialFormValues.client_group_ids = clientGroupIds
          .map((id) =>
            clientGroupsOptions.find(
              (option) => option.value.toString() === id.toString()
            )
          )
          .filter(Boolean);
      }

      // Parse state IDs
      const stateIds = queryParams.getAll("state_ids[]");
      if (stateIds.length > 0 && stateOptions.length > 0) {
        params["filter[state_ids][]"] = stateIds;
        initialFormValues.state_ids = stateIds
          .map((id) =>
            stateOptions.find(
              (option) => option.value.toString() === id.toString()
            )
          )
          .filter(Boolean);
      }

      // Parse city IDs
      const cityIds = queryParams.getAll("city_ids[]");
      if (cityIds.length > 0 && cityOptions.length > 0) {
        params["filter[city_ids][]"] = cityIds;
        initialFormValues.city_ids = cityIds
          .map((id) =>
            cityOptions.find(
              (option) => option.value.toString() === id.toString()
            )
          )
          .filter(Boolean);
      }

      // Parse location IDs
      const locationIds = queryParams.getAll("location_ids[]");
      if (locationIds.length > 0 && locationOptions.length > 0) {
        params["filter[location_ids][]"] = locationIds;
        initialFormValues.location_ids = locationIds
          .map((id) =>
            locationOptions.find(
              (option) => option.value.toString() === id.toString()
            )
          )
          .filter(Boolean);
      }

      // Parse delegate IDs
      const delegateIds = queryParams.getAll("delegate_ids[]");
      if (delegateIds.length > 0 && delegateOptions.length > 0) {
        params["filter[delegate_ids][]"] = delegateIds;
        initialFormValues.delegate_ids = delegateIds
          .map((id) =>
            delegateOptions.find(
              (option) => option.value.toString() === id.toString()
            )
          )
          .filter(Boolean);
      }

      // Parse status
      const status = queryParams.get("status");
      if (status !== null && statusOptions.length > 0) {
        params["filter[status]"] = status;
        initialFormValues.status = statusOptions.find(
          (option) => option.value?.toString() === status
        );
      }

      // Parse no_group
      const noGroup = queryParams.get("no_group");
      if (noGroup !== null && noGroupOptions.length > 0) {
        params["filter[no_group]"] = noGroup;
        initialFormValues.no_group = noGroupOptions.find(
          (option) => option.value?.toString() === noGroup
        );
      }

      return { params, initialFormValues };
    } catch (error) {
      console.error("Error parsing URL parameters:", error);
      return {
        params: {},
        initialFormValues: {
          name: "",
          client_group_ids: null,
          state_ids: null,
          city_ids: null,
          location_ids: null,
          delegate_ids: null,
          status: null,
          no_group: null,
        },
      };
    }
  };

  // Add useEffect to initialize from URL on first load
  useEffect(() => {
    if (
      clientGroupsOptions.length > 0 &&
      stateOptions.length > 0 &&
      delegateOptions.length > 0 &&
      statusOptions.length > 0 &&
      noGroupOptions.length > 0
    ) {
      const { params, initialFormValues } = parseUrlParams();

      // Set form values from URL
      reset(initialFormValues);
      setSearchParams({});

      // If we have params from URL, set them and fetch data
      if (Object.keys(params).length > 0) {
        setSearchParams(params);
      }
    }
  }, [
    clientGroupsOptions.length,
    stateOptions.length,
    delegateOptions.length,
    statusOptions.length,
    noGroupOptions.length,
    queryParams,
  ]);

  return (
    <div className="page-content">
      <Container fluid style={{ height: "100%" }}>
        <Breadcrumbs
          title={t("clients.clients")}
          breadcrumbItems={breadcrumbItems}
          isAddOptions={true}
          addTitle={t("common.add") + " " + t("clients.clients")}
          handleOrderClicks={handelAddBonds}
          canPermission="client.store"
        />
        <Card style={{ maxHeight: "90%", height: "90%", overflowY: "auto" }}>
          <CardBody>
            <TableContainer
              customComponent={
                <SearchCard
                  SearchData={SearchData}
                  control={control}
                  hadelReset={handleReset}
                  inputsArray={inputsArray}
                  register={register}
                  watch={watch}
                  setValue={setValue}
                  handelSearch={handleManualSearch}
                  hasButtonSearch={true}
                />
              }
              hideSHowGFilter={false}
              columns={columns || []}
              data={rowData || []}
              iscustomPageSize={true}
              isBordered={true}
              pageSize={10}
              isLoading={isLoading}
              customHeight={"45vh"}
              pageIndex={page}
              manualPagination={true}
              pageCount={data?.meta?.last_page || 1}
              currentPage={page}
              setPage={setPage}
            />
          </CardBody>
        </Card>
      </Container>
      {openStatsusModal && selectId && (
        <Modal isOpen={openStatsusModal} backdrop="static">
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.Attention")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button
                className="btn-sm"
                type="button"
                color="light"
                onClick={handelCLoseModal}
              >
                {t("common.no")}
              </Button>
              <Button
                onClick={() =>
                  statusIsActive ? ActiveUser(selectId) : InActive(selectId)
                }
                disabled={isDeleting}
                type="button"
                className="btn-sm"
                color="primary"
              >
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.yes")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
      )}

      <Modal
        isOpen={openDeleteMdal}
        toggle={handelCLoseModal}
        backdrop="static"
      >
        <ModalHeader toggle={handelCLoseModal}>
          {t("common.delete")} {t("clients.clients")}
        </ModalHeader>
        <ModalBody>
          <p>{t("common.delete_text")}</p>
          <ModalFooter>
            <Button
              className="btn-sm"
              type="button"
              color="light"
              onClick={handelCLoseModal}
            >
              {t("common.close")}
            </Button>
            <Button
              className="btn-sm"
              onClick={DeleteFun}
              type="button"
              color="danger"
            >
              {isDeleting ? (
                <ClipLoader color="white" size={15} />
              ) : (
                t("common.delete")
              )}
            </Button>
          </ModalFooter>
        </ModalBody>
      </Modal>
    </div>
  );
};
export default TasksTypes;
