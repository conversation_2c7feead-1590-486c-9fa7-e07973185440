import { <PERSON>, CardBody, Container } from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import { useMemo, useState } from "react";
import toastr from "toastr";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useTranslation } from "react-i18next";
import { Link, useLocation } from "react-router-dom";
import { Can } from "../../components/permissions-way/can";
import { visitQueries } from "../../apis/visits/query";
import moment from "moment";
import SearchCard from "../../components/Reports/search-card";
import { useForm } from "react-hook-form";
import { formatDate, today } from "../../helpers/api_helper";
import useSetSelectOptions from "../../hooks/use-set-select-options";
import { delegateQueries } from "../../apis/delegate/query";
import { clientsQueries } from "../../apis/clients/query";
import CustomFilterSearch from "../../components/Common/CustomFilterSearch";
import { FaInfoCircle } from "react-icons/fa";

const Visits = () => {
  const { t } = useTranslation();
  const { pathname } = useLocation();
  const [searchParams, setSearchParams] = useState({});

  const { control, watch, register, setValue, reset, handleSubmit } = useForm({
    defaultValues: {
      client: null,
      delegate_id: null,
      start_date: formatDate(today),
      end_date: formatDate(today),
    },
  });

  const {
    data: visitList,
    isLoading,
    refetch,
  } = visitQueries.useGetAll({
    ...searchParams,
  });

  // Get delegates and clients for dropdown options
  const { data: delegates } = delegateQueries.useGetAll({ status: 1 });
  const { data: clients } = clientsQueries.useGetAll({ status: 1 });

  const DelegateOptions = useSetSelectOptions({
    data: delegates?.result,
    getOption: (item) => ({ label: item.full_name, value: item.id }),
  });

  const clientsOptions = useSetSelectOptions({
    data: clients?.result,
    getOption: (item) => ({
      label: item.full_name ? item.full_name : "---" + "/" + item.company_name,
      value: item.id,
    }),
  });

  const handleSearch = handleSubmit((formData) => {
    const params = {};

    // Process client
    if (formData.client?.value) {
      params.client = formData.client.value;
    }

    // Process delegate
    if (formData.delegate_id?.value) {
      params.delegate = formData.delegate_id.value;
    }

    // Process dates
    if (formData.start_date) {
      params.start_date = formData.start_date;
    }

    if (formData.end_date) {
      params.end_date = formData.end_date;
    }

    // Update search params
    setSearchParams(params);

    // Refetch data with new params
    refetch();
  });

  const handleReset = () => {
    // Reset form fields
    reset({
      client: null,
      delegate_id: null,
      start_date: "",
      end_date: "",
    });

    // Clear search params
    setSearchParams({});

    // Refetch data with empty params
    refetch();
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const breadcrumbItems = [
    {
      title: t("visits.visit"),
      link: pathname,
    },
  ];

  const calculateDuration = (startDate, endDate) => {
    if (!startDate || !endDate) return "N/A";

    const start = moment(startDate);
    const end = moment(endDate);

    if (!start.isValid() || !end.isValid()) return "N/A";

    const duration = moment.duration(end.diff(start));
    const hours = Math.floor(duration.asHours());
    const minutes = Math.floor(duration.asMinutes()) % 60;

    return `${hours} ${t("common.hours")} ${minutes} ${t("common.minuets")}`;
  };

  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.date"),
      accessor: "date",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("clients.client"),
      accessor: "client",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.delegate"),
      accessor: "delegate",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("visits.visit_duration"),
      accessor: "visit_duration",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2">
            <Can permission={"delegate.show"}>
              <Link
                to={`/action-visit?id=${cellProps.id}?Show=true`}
                className="text-success"
              >
                {/* <i className=" ri-information-fill font-size-16"></i> */}
                <FaInfoCircle size={14} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      visitList?.result?.length > 0
        ? visitList.result
            .map((item, index) => ({
              id: item.id, // Incremental ID starting from 1
              id_toShow: index + 1,
              client: item?.client?.full_name || "----",
              delegate: item?.delegate?.full_name || "----",
              visit_duration:
                calculateDuration(item?.visit_start, item?.visit_end) || "----",
              date: item?.visit_start?.split(" ")[0] || "----",
            }))
            .reverse()
        : [],
    [visitList?.result, t]
  );

  // Define search fields for the form
  const SearchData = [
    {
      id: 0,
      label: t("clients.client"),
      type: "select",
      name: "client",
      options: clientsOptions,
      cols: 2,
      component: CustomFilterSearch,
      hasButtonSearch: true,
    },
    {
      id: 1,
      label: t("common.delegate_name"),
      type: "select",
      name: "delegate_id",
      options: DelegateOptions,
      cols: 2,
      component: CustomFilterSearch,
      hasButtonSearch: true,
    },
  ];

  // Define date input fields
  const inputsArray = [
    {
      id: 0,
      name: "start_date",
      type: "date",
      label: t("common.start_date"),
      cols: 2,
    },
    {
      id: 1,
      name: "end_date",
      type: "date",
      label: t("common.end_date"),
      cols: 2,
    },
  ];

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("visits.visit")}
          breadcrumbItems={breadcrumbItems}
        />
        <Card style={{ height: "80vh" }}>
          <CardBody>
            <SearchCard
              SearchData={SearchData}
              control={control}
              hadelReset={handleReset}
              inputsArray={inputsArray}
              register={register}
              handelSearch={handleSearch}
              watch={watch}
              setValue={setValue}
              hasButtonSearch={true}
            />
            <TableContainer
              hideSHowGFilter={false}
              columns={columns || []}
              data={rowData || []}
              isPagination={true}
              isAddOptions={false}
              iscustomPageSize={true}
              isLoading={isLoading}
              isBordered={true}
              customPageSize={10}
              hidePagination
              className="custom-header-css table align-middle table-nowrap"
              tableClassName="table-centered align-middle table-nowrap mb-0"
              theadClassName="text-muted table-light"
            />
          </CardBody>
        </Card>
      </Container>
    </div>
  );
};
export default Visits;
