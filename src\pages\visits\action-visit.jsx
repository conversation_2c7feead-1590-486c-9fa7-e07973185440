import { <PERSON><PERSON>, <PERSON>, CardB<PERSON>, Col, Container, <PERSON>, Badge } from "reactstrap";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useMemo, useState } from "react";
import toastr from "toastr";
import ClipLoader from "react-spinners/ClipLoader";
import { useLocation, <PERSON>, useNavigate } from "react-router-dom";
import { visitQueries } from "../../apis/visits/query";
import { useTranslation } from "react-i18next";
import TableContainer from "../../components/Common/TableContainer";
import moment from "moment";
import { Can } from "../../components/permissions-way/can";
import "leaflet/dist/leaflet.css";
import { <PERSON>, TileLayer, Marker, Popup } from "react-leaflet";
import L from "leaflet";

delete L.Icon.Default.prototype._getIconUrl;

L.Icon.Default.mergeOptions({
  iconRetinaUrl:
    "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png",
  iconUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png",
  shadowUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png",
});

const ActionVisit = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const selectId = Number(queryParams.get("id")?.split(",")[0].split("?")[0]);
  const { t, i18n } = useTranslation();
  // const [position, setPosition] = useState([51.505, -0.09]); // Default position (London)
  // const [selectedLocation, setSelectedLocation] = useState(null);
  const { data: visit, isLoading: isLoadingVisit } =
    visitQueries.useGetOne(selectId);

  const navigate = useNavigate();
  const breadcrumbItems = [{ title: t("visits.visit"), link: "/visits" }];

  // Function to calculate duration between start and end times
  const calculateDuration = (startDate, endDate) => {
    if (!startDate || !endDate) return "N/A";

    const start = moment(startDate);
    const end = moment(endDate);

    if (!start.isValid() || !end.isValid()) return "N/A";

    const duration = moment.duration(end.diff(start));
    const hours = Math.floor(duration.asHours());
    const minutes = Math.floor(duration.asMinutes()) % 60;

    return `${hours} ${t("common.hours")} ${minutes} ${t("common.minuets")}`;
  };

  // Function to format date and time
  const formatDateTime = (dateTime) => {
    if (!dateTime) return "N/A";
    const formatted = moment(dateTime).format("YYYY-MM-DD HH:mm:ss");
    return formatted !== "Invalid date" ? formatted : "N/A";
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const billsColumns = useMemo(
    () => [
      {
        id: "expander",
        Header: ({ getToggleAllRowsExpandedProps, isAllRowsExpanded }) => (
          <Button
            {...getToggleAllRowsExpandedProps()}
            color="link"
            size="sm"
            className="p-0"
          >
            <span style={{ marginInlineEnd: 4 }}>
              {t("reports.Expand_all")}
            </span>
            {isAllRowsExpanded ? (
              <i className="mdi mdi-chevron-down"></i>
            ) : (
              <i className="mdi mdi-chevron-right"></i>
            )}
          </Button>
        ),
        Cell: ({ row }) => {
          if (!row.original.subRows?.length) return null;
          return (
            <Button
              {...row.getToggleRowExpandedProps()}
              color="link"
              size="md"
              className="p-0"
            >
              {row.isExpanded ? (
                <i className="mdi mdi-chevron-down"></i>
              ) : (
                <i className="mdi mdi-chevron-right"></i>
              )}
            </Button>
          );
        },
        width: 30,
        disableFilters: true,
        filterable: false,
      },
      {
        Header: "#",
        width: 50,
        accessor: "id_toShow",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return <div style={{}}>{}</div>;
          }
          return value;
        },
      },
      {
        Header: t("clients.client"),
        accessor: "client",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("bills.bill_number"),
        accessor: "bill_number",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return (
            <Link to={`/action-bills?id=${row.original.id}?show=true`}>
              {value}
            </Link>
          );
        },
      },
      {
        Header: t("products.products"),
        accessor: "name",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("common.quant"),
        accessor: "quant",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("reports.total"),
        accessor: "total",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("common.discount"),
        accessor: "discount",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("common.notes"),
        accessor: "notes",
        disableFilters: true,
        filterable: false,
      },
    ],
    [t]
  );

  const billsRowData = useMemo(() => {
    if (!visit?.result?.bills) return [];

    const transformedData = [];

    visit?.result.bills.forEach((bill, billIndex) => {
      // Calculate total discount
      let totalDiscount = 0;

      // If bill has discount percentage, calculate the value
      if (bill.discount_percentage && bill.discount_percentage > 0) {
        totalDiscount = (bill.total * bill.discount_percentage) / 100;
      }
      // If bill has discount value, use it directly
      else if (bill.discount_value && bill.discount_value > 0) {
        totalDiscount = bill.discount_value;
      }

      // Add the parent row (bill)
      const parentRow = {
        id: bill.id,
        id_toShow: billIndex + 1,
        notes: bill.notes || "---",
        client: bill.client.full_name,
        bill_number: bill.bill_number || "---",
        total: bill.total,
        discount: totalDiscount > 0 ? totalDiscount.toFixed(2) : "---",
      };

      // Add sub-rows (details)
      parentRow.subRows = bill.details.map((detail, detailIndex) => {
        // Calculate discount for individual product if it exists
        let detailDiscount = 0;
        if (detail.discount_percentage && detail.discount_percentage > 0) {
          detailDiscount = (detail.total * detail.discount_percentage) / 100;
        } else if (detail.discount_value && detail.discount_value > 0) {
          detailDiscount = detail.discount_value;
        } else if (detail.discount) {
          detailDiscount = detail.discount;
        }

        return {
          id: `${bill.id}-${detail.id}`,
          id_toShow: `${billIndex + 1}.${detailIndex + 1}`,
          name: detail.product.name || "---",
          quant: detail.quant || "---",
          total: detail.total || "---",
          discount: detailDiscount > 0 ? detailDiscount.toFixed(2) : "---",
        };
      });

      transformedData.push(parentRow);
    });

    return transformedData.reverse();
  }, [visit?.result.bills, i18n.language]);

  const contractsColumns = useMemo(
    () => [
      {
        id: "expander",
        Header: ({ getToggleAllRowsExpandedProps, isAllRowsExpanded }) => (
          <Button
            {...getToggleAllRowsExpandedProps()}
            color="link"
            size="sm"
            className="p-0"
          >
            <span style={{ marginInlineEnd: 4 }}>
              {t("reports.Expand_all")}
            </span>
            {isAllRowsExpanded ? (
              <i className="mdi mdi-chevron-down"></i>
            ) : (
              <i className="mdi mdi-chevron-right"></i>
            )}
          </Button>
        ),
        Cell: ({ row }) => {
          if (!row.original.subRows?.length) return null;
          return (
            <Button
              {...row.getToggleRowExpandedProps()}
              color="link"
              size="md"
              className="p-0"
            >
              {row.isExpanded ? (
                <i className="mdi mdi-chevron-down"></i>
              ) : (
                <i className="mdi mdi-chevron-right"></i>
              )}
            </Button>
          );
        },
        width: 30,
        disableFilters: true,
        filterable: false,
      },
      {
        Header: "#",
        width: 50,
        accessor: "id_toShow",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return <div style={{}}>{}</div>;
          }
          return value;
        },
      },
      {
        Header: t("clients.client"),
        accessor: "client",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("contracts.contract_number"),
        accessor: "contract_number",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return (
            <Link to={`/action-contract?id=${row.original.id}?show=true`}>
              {value}
            </Link>
          );
        },
      },
      {
        Header: t("products.products"),
        accessor: "name",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("common.quant"),
        accessor: "quant",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("reports.total"),
        accessor: "total",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("common.discount"),
        accessor: "discount",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("common.notes"),
        accessor: "notes",
        disableFilters: true,
        filterable: false,
      },
    ],
    [t]
  );

  const contractssRowData = useMemo(() => {
    if (!visit?.result?.contracts) return [];

    const transformedData = [];

    visit.result?.contracts.forEach((contract, contractIndex) => {
      // Calculate total discount
      let totalDiscount = 0;

      // If contract has discount percentage, calculate the value
      if (contract.discount_percentage && contract.discount_percentage > 0) {
        totalDiscount = (contract.total * contract.discount_percentage) / 100;
      }
      // If contract has discount value, use it directly
      else if (contract.discount_value && contract.discount_value > 0) {
        totalDiscount = contract.discount_value;
      }

      // Add the parent row (contract)
      const parentRow = {
        id: contract.id,
        id_toShow: contractIndex + 1,
        notes: contract.notes || "---",
        client: contract.client.full_name,
        contract_number: contract.contract_number || "---",
        total: contract.total,
        discount: totalDiscount > 0 ? totalDiscount.toFixed(2) : "---",
      };

      // Add sub-rows (details)
      parentRow.subRows = contract.details.map((detail, detailIndex) => {
        // Calculate discount for individual product if it exists
        let detailDiscount = 0;
        if (detail.discount_percentage && detail.discount_percentage > 0) {
          detailDiscount = (detail.total * detail.discount_percentage) / 100;
        } else if (detail.discount_value && detail.discount_value > 0) {
          detailDiscount = detail.discount_value;
        } else if (detail.discount) {
          detailDiscount = detail.discount;
        }

        return {
          id: `${contract.id}-${detail.id}`,
          id_toShow: `${contractIndex + 1}.${detailIndex + 1}`,
          name: detail.product.name || "---",
          quant: detail.quant || "---",
          total: detail.total || "---",
          discount: detailDiscount > 0 ? detailDiscount.toFixed(2) : "---",
        };
      });

      transformedData.push(parentRow);
    });

    return transformedData.reverse();
  }, [visit?.result, i18n.language]);

  const bondsColumns = useMemo(() => {
    const baseColumns = [
      {
        Header: "#",
        width: 50,
        accessor: "id_toShow",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("bonds.voucher_number"),
        accessor: "bond_number",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => (
          <Link to={`/action-bonds?id=${row.original.id}?show=true`}>
            {value}
          </Link>
        ),
      },
      {
        Header: t("bonds.voucher_date"),
        accessor: "bond_date",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.total"),
        accessor: "total",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("bonds.voucher_type"),
        accessor: "bond_type",
        disableFilters: true,
        filterable: false,
      },
    ];

    return baseColumns;
  }, [i18n.language]);

  const offerColumns = useMemo(
    () => [
      {
        id: "expander",
        Header: ({ getToggleAllRowsExpandedProps, isAllRowsExpanded }) => (
          <Button
            {...getToggleAllRowsExpandedProps()}
            color="link"
            size="sm"
            className="p-0"
          >
            <span style={{ marginInlineEnd: 4 }}>
              {t("reports.Expand_all")}
            </span>
            {isAllRowsExpanded ? (
              <i className="mdi mdi-chevron-down"></i>
            ) : (
              <i className="mdi mdi-chevron-right"></i>
            )}
          </Button>
        ),
        Cell: ({ row }) => {
          if (!row.original.subRows?.length) return null;
          return (
            <Button
              {...row.getToggleRowExpandedProps()}
              color="link"
              size="md"
              className="p-0"
            >
              {row.isExpanded ? (
                <i className="mdi mdi-chevron-down"></i>
              ) : (
                <i className="mdi mdi-chevron-right"></i>
              )}
            </Button>
          );
        },
        width: 30,
        disableFilters: true,
        filterable: false,
      },
      {
        Header: "#",
        width: 50,
        accessor: "id_toShow",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return <div style={{}}>{}</div>;
          }
          return value;
        },
      },
      {
        Header: t("clients.client"),
        accessor: "client",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("products.products"),
        accessor: "name",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("common.quant"),
        accessor: "quant",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("reports.total"),
        accessor: "total",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("common.discount"),
        accessor: "discount",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("common.notes"),
        accessor: "notes",
        disableFilters: true,
        filterable: false,
      },

      {
        Header: t("common.actions"),
        accessor: (cellProps) => {
          return (
            <Can permission={"offer.show"}>
              <div
                onClick={() => {
                  navigate(`/action-offer-price/?id=${cellProps.id}?Show=true`);
                }}
                className="cursor-pointers"
              >
                <i className=" ri-information-fill font-size-16"></i>
              </div>
            </Can>
          );
        },
        disableFilters: true,
        filterable: false,
      },
    ],
    [t]
  );

  const offerRowData = useMemo(() => {
    if (!visit?.result?.offers) return [];

    const transformedData = [];

    visit?.result.offers.forEach((offer, offerIndex) => {
      // Calculate total discount
      let totalDiscount = 0;

      // If offer has discount percentage, calculate the value
      if (offer.discount_percentage && offer.discount_percentage > 0) {
        totalDiscount = (offer.total * offer.discount_percentage) / 100;
      }
      // If offer has discount value, use it directly
      else if (offer.discount_value && offer.discount_value > 0) {
        totalDiscount = offer.discount_value;
      }

      // Add the parent row (offer)
      const parentRow = {
        id: offer.id,
        id_toShow: offerIndex + 1,
        notes: offer.notes || "---",
        client: offer.clients[0]?.full_name,
        contract_number: offer.contract_number || "---",
        total: offer.total,
        discount: totalDiscount > 0 ? totalDiscount.toFixed(2) : "---",
      };

      // Add sub-rows (details)
      parentRow.subRows = offer.offer_details.map((detail, detailIndex) => {
        // Calculate discount for individual product if it exists
        let detailDiscount = 0;
        if (detail.discount_percentage && detail.discount_percentage > 0) {
          detailDiscount = (detail.total * detail.discount_percentage) / 100;
        } else if (detail.discount_value && detail.discount_value > 0) {
          detailDiscount = detail.discount_value;
        } else if (detail.discount) {
          detailDiscount = detail.discount;
        }

        return {
          id: `${offer.id}-${detail.product_id}`,
          id_toShow: `${offerIndex + 1}.${detailIndex + 1}`,
          name: detail.product_name || "---",
          quant: detail.quant || "---",
          total: detail.total || "---",
          discount: detailDiscount > 0 ? detailDiscount.toFixed(2) : "---",
        };
      });

      transformedData.push(parentRow);
    });

    return transformedData.reverse();
  }, [visit?.result, i18n.language]);

  const bondsRowData = useMemo(
    () =>
      visit?.result?.bonds?.length > 0
        ? visit?.result?.bonds
            .map((item, index) => {
              // Calculate discount if available
              let discount = "---";
              if (item.discount_percentage && item.discount_percentage > 0) {
                discount = (
                  (item.total * item.discount_percentage) /
                  100
                ).toFixed(2);
              } else if (item.discount_value && item.discount_value > 0) {
                discount = item.discount_value.toFixed(2);
              } else if (item.discount) {
                discount = item.discount.toFixed(2);
              }

              return {
                id: item.id,
                id_toShow: index + 1,
                client_id: item?.client?.full_name,
                bond_number: item.bond_number,
                bond_date: item.bond_date,
                total: item.total,
                discount: discount,
                bond_type:
                  i18n.language === "eng"
                    ? item?.bond_type?.title?.en
                    : item?.bond_type?.title?.ar,
              };
            })
            .reverse()
        : [],
    [visit?.result, i18n.language]
  );

  console.log("billsRowData", billsRowData);

  return (
    <div className="page-content" lang={i18n.language === "ar" ? "ar" : "en"}>
      <Container fluid>
        <Breadcrumbs
          title={t("visits.visit")}
          breadcrumbItems={breadcrumbItems}
        />
        <Row>
          <Col lg={12}>
            {/* Visit Summary Card */}
            {isLoadingVisit ? (
              <div className="text-center mb-4">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : (
              <Card className="mb-4 shadow-none border-0">
                <CardBody>
                  <h5 className="card-title mb-3">
                    {t("visits.visit")} {t("common.details")}
                  </h5>
                  <Row>
                    <Col md={3} lg={3} className="mb-3">
                      <div className="d-flex flex-column">
                        <span className="text-muted mb-1">
                          {t("common.delegate")}:
                        </span>
                        <span className="fw-medium">
                          {visit?.result?.delegate?.full_name || "----"}
                        </span>
                      </div>
                    </Col>
                    <Col md={3} lg={3} className="mb-3">
                      <div className="d-flex flex-column">
                        <span className="text-muted mb-1">
                          {t("clients.client")}:
                        </span>
                        <span className="fw-medium">
                          {visit?.result?.client?.full_name || "----"}
                        </span>
                      </div>
                    </Col>
                    <Col md={3} lg={3} className="mb-3">
                      <div className="d-flex flex-column">
                        <span className="text-muted mb-1">
                          {t("visits.visit_duration")}:
                        </span>
                        <span className="fw-medium">
                          {calculateDuration(
                            visit?.result?.visit_start,
                            visit?.result?.visit_end
                          )}
                        </span>
                      </div>
                    </Col>
                    <Col md={3} className="mb-3">
                      <div className="d-flex flex-column">
                        <span className="text-muted mb-1">
                          {t("common.date")}:
                        </span>
                        <span className="fw-medium">
                          {
                            formatDateTime(visit?.result?.visit_start)?.split(
                              " "
                            )[0]
                          }
                        </span>
                      </div>
                    </Col>
                  </Row>
                </CardBody>
              </Card>
            )}
          </Col>
          <Col lg={12}>
            <Card className="shadow-none border-0">
              <CardBody className="small-text">
                {isLoadingVisit ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <TableContainer
                    columns={contractsColumns || []}
                    data={contractssRowData || []}
                    customComponent={
                      <h4 className="small-font">{t("contracts.contracts")}</h4>
                    }
                    isPagination={true}
                    hideSHowGFilter={true}
                    hidePagination={true}
                    isSmall
                    iscustomPageSize={true}
                    isBordered={true}
                    customPageSize={5}
                    pageOptions={[5, 10, 20]}
                    className="custom-header-css table align-middle table-nowrap small-text"
                    tableClassName="table-centered align-middle table-nowrap mb-0 small-font"
                    theadClassName="text-muted table-light"
                  />
                )}
                {!isLoadingVisit && billsRowData.length > 0 && (
                  <div className="mt-3 border-top pt-2">
                    <div className="d-flex justify-content-end">
                      <div className="d-flex flex-row align-items-center gap-4 border p-3 rounded bg-light">
                        <div className="d-flex flex-column align-items-center">
                          <span className="text-muted small">
                            {t("reports.total")}
                          </span>
                          <span className="fw-medium">
                            {billsRowData
                              .reduce((sum, bill) => {
                                const totalValue = parseFloat(bill.total) || 0;
                                return sum + totalValue;
                              }, 0)
                              .toFixed(2)}
                          </span>
                        </div>
                        <div className="d-flex flex-column align-items-center">
                          <span className="text-muted small">
                            {t("discount")}
                          </span>
                          <span className="fw-medium">
                            {billsRowData
                              .reduce((sum, bill) => {
                                const discountValue =
                                  bill.discount !== "---"
                                    ? parseFloat(bill.discount)
                                    : 0;
                                return sum + discountValue;
                              }, 0)
                              .toFixed(2)}
                          </span>
                        </div>
                        <div className="d-flex flex-column align-items-center px-3 py-2 bg-light-subtle rounded-3 border border-light">
                          <span className="text-muted small">Net Total</span>
                          <span className="fw-medium">
                            {billsRowData
                              .reduce((sum, bill) => {
                                const totalValue = parseFloat(bill.total) || 0;
                                const discountValue =
                                  bill.discount !== "---"
                                    ? parseFloat(bill.discount)
                                    : 0;
                                return sum + (totalValue - discountValue);
                              }, 0)
                              .toFixed(2)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardBody>
            </Card>
          </Col>
          <Col lg={6}>
            <Card className="shadow-none border-0">
              <CardBody className="small-text">
                {isLoadingVisit ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <TableContainer
                    columns={contractsColumns || []}
                    data={contractssRowData || []}
                    customComponent={
                      <h4 className="small-font">{t("contracts.contracts")}</h4>
                    }
                    isPagination={true}
                    hideSHowGFilter={true}
                    hidePagination={true}
                    isSmall
                    iscustomPageSize={true}
                    isBordered={true}
                    customPageSize={5}
                    pageOptions={[5, 10, 20]}
                    className="custom-header-css table align-middle table-nowrap small-text"
                    tableClassName="table-centered align-middle table-nowrap mb-0 small-font"
                    theadClassName="text-muted table-light"
                  />
                )}
                {!isLoadingVisit && contractssRowData.length > 0 && (
                  <div className="mt-3 border-top pt-2">
                    <div className="d-flex justify-content-end">
                      <div className="d-flex flex-row align-items-center gap-4 border p-3 rounded bg-light">
                        <div className="d-flex flex-column align-items-center">
                          <span className="text-muted small">
                            {t("reports.total")}
                          </span>
                          <span className="fw-medium">
                            {contractssRowData
                              .reduce((sum, contract) => {
                                const totalValue =
                                  parseFloat(contract.total) || 0;
                                return sum + totalValue;
                              }, 0)
                              .toFixed(2)}
                          </span>
                        </div>
                        <div className="d-flex flex-column align-items-center">
                          <span className="text-muted small">
                            {t("discount")}
                          </span>
                          <span className="fw-medium">
                            {contractssRowData
                              .reduce((sum, contract) => {
                                const discountValue =
                                  contract.discount !== "---"
                                    ? parseFloat(contract.discount)
                                    : 0;
                                return sum + discountValue;
                              }, 0)
                              .toFixed(2)}
                          </span>
                        </div>
                        <div className="d-flex flex-column align-items-center px-3 py-2 bg-light-subtle rounded-3 border border-light">
                          <span className="text-muted small">Net Total</span>
                          <span className="fw-medium">
                            {contractssRowData
                              .reduce((sum, contract) => {
                                const totalValue =
                                  parseFloat(contract.total) || 0;
                                const discountValue =
                                  contract.discount !== "---"
                                    ? parseFloat(contract.discount)
                                    : 0;
                                return sum + (totalValue - discountValue);
                              }, 0)
                              .toFixed(2)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardBody>
            </Card>
          </Col>

          <Col lg={6}>
            <Card className="shadow-none border-0">
              <CardBody className="small-text">
                {isLoadingVisit ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <TableContainer
                    columns={offerColumns || []}
                    data={offerRowData || []}
                    customComponent={
                      <h4 className="small-font">{t("offer.quotation")}</h4>
                    }
                    isPagination={true}
                    hideSHowGFilter={true}
                    hidePagination={true}
                    isSmall
                    iscustomPageSize={true}
                    isBordered={true}
                    customPageSize={5}
                    pageOptions={[5, 10, 20]}
                    className="custom-header-css table align-middle table-nowrap small-text"
                    tableClassName="table-centered align-middle table-nowrap mb-0 small-font"
                    theadClassName="text-muted table-light"
                  />
                )}
                {!isLoadingVisit && offerRowData.length > 0 && (
                  <div className="mt-3 border-top pt-2">
                    <div className="d-flex justify-content-end">
                      <div className="d-flex flex-row align-items-center gap-4 border p-3 rounded bg-light">
                        <div className="d-flex flex-column align-items-center">
                          <span className="text-muted small">
                            {t("reports.total")}
                          </span>
                          <span className="fw-medium">
                            {offerRowData
                              .reduce((sum, offer) => {
                                const totalValue = parseFloat(offer.total) || 0;
                                return sum + totalValue;
                              }, 0)
                              .toFixed(2)}
                          </span>
                        </div>
                        <div className="d-flex flex-column align-items-center">
                          <span className="text-muted small">
                            {t("discount")}
                          </span>
                          <span className="fw-medium">
                            {offerRowData
                              .reduce((sum, offer) => {
                                const discountValue =
                                  offer.discount !== "---"
                                    ? parseFloat(offer.discount)
                                    : 0;
                                return sum + discountValue;
                              }, 0)
                              .toFixed(2)}
                          </span>
                        </div>
                        <div className="d-flex flex-column align-items-center px-3 py-2 bg-light-subtle rounded-3 border border-light">
                          <span className="text-muted small">Net Total</span>
                          <span className="fw-medium">
                            {offerRowData
                              .reduce((sum, offer) => {
                                const totalValue = parseFloat(offer.total) || 0;
                                const discountValue =
                                  offer.discount !== "---"
                                    ? parseFloat(offer.discount)
                                    : 0;
                                return sum + (totalValue - discountValue);
                              }, 0)
                              .toFixed(2)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardBody>
            </Card>
          </Col>
          <Col lg={12}>
            <Card className="shadow-none border-0">
              <CardBody className="small-text">
                {isLoadingVisit ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <TableContainer
                    columns={bondsColumns || []}
                    data={bondsRowData || []}
                    customComponent={
                      <h4 className="small-font">{t("bonds.voucher")}</h4>
                    }
                    hideSHowGFilter={true}
                    hidePagination={true}
                    isPagination={true}
                    isSmall
                    iscustomPageSize={true}
                    isBordered={true}
                    customPageSize={5}
                    pageOptions={[5, 10, 20]}
                    className="custom-header-css table align-middle table-nowrap small-text"
                    tableClassName="table-centered align-middle table-nowrap mb-0 small-font"
                    theadClassName="text-muted table-light"
                  />
                )}
                {!isLoadingVisit && bondsRowData.length > 0 && (
                  <div className="mt-3 border-top pt-2">
                    <div className="d-flex justify-content-end">
                      <div className="d-flex flex-row align-items-center gap-4 border p-3 rounded bg-light">
                        <div className="d-flex flex-column align-items-center">
                          <span className="text-muted small">
                            {t("reports.total")}
                          </span>
                          <span className="fw-medium">
                            {bondsRowData
                              .reduce((sum, bond) => {
                                const totalValue = parseFloat(bond.total) || 0;
                                return sum + totalValue;
                              }, 0)
                              .toFixed(2)}
                          </span>
                        </div>
                        <div className="d-flex flex-column align-items-center px-3 py-2 bg-light-subtle rounded-3 border border-light">
                          <span className="text-muted small">Net Total</span>
                          <span className="fw-medium">
                            {bondsRowData
                              .reduce((sum, bond) => {
                                const totalValue = parseFloat(bond.total) || 0;
                                const discountValue =
                                  bond.discount !== "---"
                                    ? parseFloat(bond.discount)
                                    : 0;
                                return sum + (totalValue - discountValue);
                              }, 0)
                              .toFixed(2)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardBody>
            </Card>
          </Col>

          <Card>
            <CardBody>
              {visit?.result?.checked_in?.lat &&
                visit?.result?.checked_in?.lng && (
                  <div className="my-2">
                    <label className="form-label" style={{ fontSize: 16 }}>
                      {t("common.visit_loation_start")}
                    </label>
                    <div style={{ height: "50vh", width: "100%" }}>
                      <Map
                        center={[
                          visit?.result?.checked_in?.lat,
                          visit?.result?.checked_in?.lng,
                        ]}
                        zoom={13}
                        attributionControl={false}
                        style={{ height: "100%", width: "100%" }}
                      >
                        <TileLayer
                          attribution='&copy; <a href="http://osm.org/copyright">OpenStreetMap</a> contributors'
                          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                        />
                        {/* {selectedLocation && ( */}
                        <Marker
                          position={[
                            visit?.result?.checked_in?.lat,
                            visit?.result?.checked_in?.lng,
                          ]}
                        >
                          <Popup>Selected location</Popup>
                        </Marker>
                        {/* )} */}
                      </Map>
                    </div>
                  </div>
                )}

              {visit?.result?.checked_out?.lat &&
                visit?.result?.checked_out?.lng && (
                  <div className="my-2">
                    <label className="form-label" style={{ fontSize: 16 }}>
                      {t("common.visit_loation_end")}
                    </label>
                    <div style={{ height: "50vh", width: "100%" }}>
                      <Map
                        center={[
                          visit?.result?.checked_out?.lat,
                          visit?.result?.checked_out?.lng,
                        ]}
                        zoom={13}
                        attributionControl={false}
                        style={{ height: "100%", width: "100%" }}
                      >
                        <TileLayer
                          attribution='&copy; <a href="http://osm.org/copyright">OpenStreetMap</a> contributors'
                          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                        />
                        {/* {selectedLocation && ( */}
                        <Marker
                          position={[
                            visit?.result?.checked_out?.lat,
                            visit?.result?.checked_out?.lng,
                          ]}
                        >
                          <Popup>Selected location</Popup>
                        </Marker>
                        {/* )} */}
                      </Map>
                    </div>
                  </div>
                )}
            </CardBody>
          </Card>
          <Col lg={12}>
            <div className="d-flex justify-content-start mb-2">
              <Button type="button" color="light" onClick={() => navigate(-1)}>
                {t("common.close")}
              </Button>
            </div>
          </Col>
        </Row>
      </Container>
    </div>
  );
};
export default ActionVisit;
