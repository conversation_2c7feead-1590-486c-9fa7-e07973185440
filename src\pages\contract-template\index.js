import {
  Card,
  CardBody,
  Container,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import { useEffect, useMemo, useState } from "react";
import toastr from "toastr";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { handleBackendErrors, hasPermission } from "../../helpers/api_helper";
import { useTranslation } from "react-i18next";
import { Can } from "../../components/permissions-way/can";
import ActionContractTemplate from "../../components/Common/actionContractTemplate";
import { contractTemplateQueries } from "../../apis/contract_templete/query";
import TypesModel from "../../components/Common/types-model";
import DeleteModal from "../../components/Common/DeleteModal";
import { contractTemplateAPis } from "../../apis/contract_templete/api";
import SectionsPage from "../sections";
import { HiDotsVertical } from "react-icons/hi";
import { FaPenToSquare } from "react-icons/fa6";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaBook } from "react-icons/fa";

const ContractTemplate = () => {
  const [pagination, setPagination] = useState(1);
  const { t, i18n } = useTranslation();
  const { data, isLoading, refetch } = contractTemplateQueries.useGetAll({
    limit: 10,
    page: pagination,
  });

  const [selctedPage, setSelectedPage] = useState("template");

  const [selectId, setSelectId] = useState(null);
  const [templateId, setTemplateId] = useState(null);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [openMenu, setOpenMenu] = useState(false);
  const [open, setOpen] = useState(false);
  const [isShow, setIsShow] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Add effect to watch language changes
  useEffect(() => {
    refetch();
  }, [i18n.language]);

  const toggleMenu = (id) => {
    setOpenMenu((prev) => (prev === id ? null : id)); // Open menu for current row, close if same row is clicked again
  };

  const handelCLoseModal = () => {
    setOpen(false);
    setIsShow(false);
    setSelectId(0);
    setTemplateId(0);
    setOpenDeleteModal(false);
    refetch();
    // setOpenSection(false);
  };

  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const handelSelectId = (id) => {
    setSelectId(id);
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const breadcrumbItems = [
    {
      title: t("common.contract_template"),
      //   link: pathname,
    },
  ];

  const columns = useMemo(() => {
    const baseColumns = [
      {
        Header: "#",
        width: 50,
        accessor: "id_toShow",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.name"),
        accessor: "name",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("types.contract_types.contract_type"),
        accessor: "contract_type",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.language"),
        accessor: "language",
        disableFilters: true,
        filterable: false,
      },
    ];

    const permissions = [
      "task.update",
      "task.destroy",
      "task.accept_task",
      "task.transfer_task",
      "task.cancel_task",
      "task.done_task",
      "task.show",
    ];

    // Check if user has any of the required permissions
    const hasPer = permissions.some((permission) => hasPermission(permission));
    // Only add actions column if user has any permission
    if (hasPer) {
      baseColumns.push({
        Header: t("common.actions"),
        accessor: (cellProps) => {
          console.log("cellProps.status", cellProps.statusCond);
          return (
            <div style={{ position: "relative" }}>
              <Dropdown
                isOpen={openMenu === cellProps.id}
                toggle={() => toggleMenu(cellProps.id)}
                className="position-absolute left-0"
              >
                <DropdownToggle tag="i" className="arrow-none card-drop">
                  {/* <i className="mdi mdi-dots-vertical"></i> */}
                  <HiDotsVertical size={18} />
                </DropdownToggle>
                <DropdownMenu
                  style={{ height: "fit-content", background: "#fff" }}
                  className="dropdown-menu-end text-center"
                >
                  <Can permission={"task.update"}>
                    <DropdownItem
                      onClick={() => {
                        setSelectId(cellProps.id);
                        setOpen(true);
                      }}
                      className="text-success"
                    >
                      {/* <i className=" ri-pencil-fill align-middle me-2 text-success"></i> */}
                      <FaPenToSquare size={14} className="mx-2" />

                      {t("common.update")}
                    </DropdownItem>
                  </Can>
                  <Can permission={"task.destroy"}>
                    <DropdownItem
                      onClick={() => {
                        handelOpenModal();
                        handelSelectId(cellProps.id);
                      }}
                      className="text-danger"
                    >
                      {/* <i className=" ri-delete-bin-fill align-middle me-2 text-danger"></i> */}
                      <MdDeleteSweep size={18} className="mx-2" />

                      {t("common.delete")}
                    </DropdownItem>
                  </Can>

                  <Can permission={"task.cancel_task"}>
                    <DropdownItem
                      onClick={() => {
                        // setOpenCancelModal(true);
                        // setSelectId(cellProps.task_transaction_id);
                        setTemplateId(cellProps.id);
                        setSelectedPage("section");
                      }}
                      className="text-danger"
                    >
                      {/* <i className="ri-eye-fill align-middle me-1 text-warning"></i> */}
                      <FaBook size={14} className="mx-2s" />
                      {t("common.sections")}
                    </DropdownItem>
                  </Can>
                  <Can permission={"task.show"}>
                    <DropdownItem
                      onClick={() => {
                        setSelectId(cellProps.id);
                        setIsShow(true);
                        setOpen(true);
                      }}
                      className="text-warning"
                    >
                      {/* <i className="ri-slideshow-2-fill align-middle me-2"></i> */}
                      <FaInfoCircle size={14} className="mx-2" />

                      {t("common.show")}
                    </DropdownItem>
                  </Can>
                </DropdownMenu>
              </Dropdown>
            </div>
          );
        },
        disableFilters: true,
        filterable: false,
      });
    }

    return baseColumns;
  }, [openMenu, t, i18n.language]);

  const rowData = useMemo(
    () =>
      data?.result?.length > 0
        ? data?.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: (pagination - 1) * 10 + index + 1,
              name: item.name,
              contract_type: item.contract_type.title,
              language:
                item.language === 1 ? t("common.arabic") : t("common.english"),
            }))
            .reverse()
        : [],
    [data?.result, i18n.language, t, pagination]
  );

  const deleteFUn = async () => {
    try {
      setIsDeleting(true);
      const response = await contractTemplateAPis.deleteFu({
        id: selectId,
      });
      refetch();
      toastr.success(response?.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  console.log("templateId", templateId);

  return selctedPage === "template" ? (
    <div>
      <Container fluid>
        <Breadcrumbs
          title={t("common.contract_template")}
          breadcrumbItems={breadcrumbItems}
          addTitle={t("common.add") + " " + t("common.contract_template")}
          canPermission="task.store"
          isAddOptions={true}
          handleOrderClicks={() => setOpen(true)}
        />
        <Card>
          <CardBody>
            <TableContainer
              hideSHowGFilter={false}
              columns={columns || []}
              data={rowData || []}
              setPage={setPagination}
              pageCount={data?.meta?.last_page}
              currentPage={pagination}
              isLoading={isLoading}
            />
          </CardBody>
        </Card>
        <DeleteModal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          onDelete={deleteFUn}
          itemName={t("common.contract_template")}
          isDeleting={isDeleting}
        />
      </Container>
      <TypesModel
        open={open}
        handelClose={() => {
          setSelectId(null);
          setTemplateId(null);
          setOpen(false);
        }}
        hideAll={true}
        content={
          <div>
            <ActionContractTemplate
              handelClose={() => {
                setSelectId(null);
                setTemplateId(null);
                setOpen(false);
                setIsShow(false);
                refetch();
              }}
              isShow={isShow}
              selectId={selectId}
              refetch={refetch}
            />
          </div>
        }
      />
    </div>
  ) : (
    <SectionsPage
      handelClose={() => {
        handelCLoseModal();
      }}
      isShow={isShow}
      selectId={selectId}
      refetch={refetch}
      templateId={templateId}
      setSelectedPage={setSelectedPage}
    />
  );
};
export default ContractTemplate;
