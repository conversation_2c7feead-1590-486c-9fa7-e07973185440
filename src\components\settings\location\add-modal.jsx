import { Controller, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import <PERSON><PERSON><PERSON>oader from "react-spinners/ClipLoader";
import {
  Button,
  Col,
  Label,
  Modal,
  ModalBody,
  ModalHeader,
  Row,
} from "reactstrap";
import Select from "react-select";
import { locationsAPis } from "../../../apis/locations/api";
import { handleBackendErrors } from "../../../helpers/api_helper";
import { useEffect, useState } from "react";
import toastr from "toastr";
import { locationQueries } from "../../../apis/locations/query";
import { citiesQueries } from "../../../apis/cities/query";

const AddLocationModel = ({
  openModal,
  handelCLoseModal,
  selectedId,
  refetch,
  city_id,
  setValue,
}) => {
  const { t } = useTranslation();
  const [optionGroup, setOptionGroup] = useState([]); // Assuming you have a way to populate this

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const { data: cities } = citiesQueries.useGetAll({});

  const { data: location, isLoading: isLoadingLocation } =
    locationQueries.useGet({
      id: selectedId,
    });

  useEffect(() => {
    if (cities?.result?.length > 0) {
      setOptionGroup(
        cities.result.map((item) => ({
          label: item.name,
          value: item.id,
        }))
      );
    }
  }, [cities?.result]);

  const addFun = async (data) => {
    try {
      const response = await locationsAPis.add({
        payload: { name: data.name, city_id: data.city_id.value },
      });
      toastr.success(response.message);
      if (setValue) {
        const responseData = response?.result;
        setValue("location_id", {
          label: responseData?.name,
          value: responseData?.id,
        });
      }
      handelCLoseModal();
      refetch();
      reset();
      reset({ name: "" });
    } catch (errors) {
      handleBackendErrors({ error: errors, setError });
    }
  };

  const UpdateFun = async (data) => {
    try {
      const response = await locationsAPis.update({
        payload: { name: data.name, city_id: data.city_id.value },
        id: selectedId,
      });
      toastr.success(response.message);
      refetch();
      reset({ name: "" });
      handelCLoseModal();
    } catch (errors) {
      handleBackendErrors({ error: errors, setError });
    }
  };

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    watch,
    register,
    setError,
    control,
  } = useForm({ defaultValues: { name: "" } });

  useEffect(() => {
    if (selectedId && location?.result) {
      // const selectCity = cities?.result?.find(
      //   (item) => item.id === location.result.city_id
      // );

      reset({
        name: location.result.name,
        city_id: {
          label: location?.result?.city?.name,
          value: location?.result?.city?.id,
        },
      });
    }
  }, [location?.result]);

  useEffect(() => {
    if (city_id) {
      const selectedCity = cities?.result?.find((item) => item.id === city_id);
      console.log("selectedCity", city_id, cities?.result);
      reset({
        city_id: { label: selectedCity?.name, value: selectedCity?.id },
      });
    }
  }, [city_id, openModal]);

  return (
    <Modal isOpen={openModal} toggle={handelCLoseModal} backdrop="static">
      <ModalHeader toggle={handelCLoseModal}>
        {selectedId
          ? t("common.update") + " " + t("common.locations")
          : t("common.add") + " " + t("common.locations")}
      </ModalHeader>
      <ModalBody>
        <Row>
          <form
            onSubmit={
              selectedId ? handleSubmit(UpdateFun) : handleSubmit(addFun)
            }
          >
            {isLoadingLocation ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : (
              <>
                <Row>
                  <Col xs={12} className="mb-2">
                    <div>
                      <Label className="form-label" htmlFor="total">
                        {t("common.select")} {t("common.city")}
                      </Label>
                    </div>
                    <Controller
                      name="city_id"
                      control={control}
                      defaultValue={[]}
                      render={({ field }) => (
                        <Select
                          {...field}
                          isClearable
                          options={optionGroup}
                          isMulti={false}
                          placeholder="----"
                          isDisabled={!!city_id}
                          // isDisabled={isShow}
                          styles={{
                            menuList: (props) => ({
                              ...props,
                              paddingBottom: 10,
                              height: "100px",
                            }),
                            menu: (props) => ({
                              ...props,
                              height: "100px",
                            }),
                          }}
                        />
                      )}
                    />
                  </Col>
                  <Col xs={12}>
                    <div className="mb-2">
                      <Label className="form-label" htmlFor="title-in-english">
                        {t("common.name_of_location")}
                      </Label>
                      <input
                        {...register("name", { required: true })}
                        placeholder="---"
                        type="text"
                        className={`form-control ${
                          errors.name ? "is-invalid" : ""
                        }`}
                        id="title-in-english"
                      />
                      {errors.name && (
                        <div className="invalid-feedback">
                          {errors.name.message}
                        </div>
                      )}
                    </div>
                  </Col>
                </Row>
              </>
            )}
            <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
              <Button
                type="button"
                color="light"
                className="btn-sm"
                onClick={handelCLoseModal}
              >
                {t("common.close")}
              </Button>
              <Button
                color="primary"
                className="btn-sm waves-effect waves-light primary-button"
                type="submit"
                disabled={isSubmitting || (!watch("name") && !watch("name"))}
              >
                {isSubmitting ? (
                  <ClipLoader color="white" size={15} />
                ) : selectedId ? (
                  t("common.update")
                ) : (
                  t("common.add")
                )}
              </Button>
            </div>
          </form>
        </Row>
      </ModalBody>
    </Modal>
  );
};
export default AddLocationModel;
