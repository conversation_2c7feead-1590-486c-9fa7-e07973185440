import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { <PERSON><PERSON>, Card, CardBody, Col, Container, Label, Row } from "reactstrap";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";
import toastr from "toastr";
import ClipLoader from "react-spinners/ClipLoader";
import { useLocation, useNavigate } from "react-router-dom";
import { UsersQueries } from "../../apis/cars/query";
import { USERSAPis } from "../../apis/cars/api";
import {
  formatDate,
  handleBackendErrors,
  today,
} from "../../helpers/api_helper";
import { useTranslation } from "react-i18next";
import CustomInput from "../../components/Common/Input";
import CustomSelect from "../../components/Common/Select";
import CustomTextArea from "../../components/Common/textArea";
const CarsActions = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const selectId = Number(queryParams.get("id")?.split("?")[0]);
  const isShow = queryParams.get("id")?.split("?")[1];

  const { pathname } = useLocation();
  const { data, isLoading } = UsersQueries.useGetUser({
    id: Number(selectId),
  });

  const { t, i18n } = useTranslation();

  const schema = yup
    .object({
      name: yup.string().required(t("common.field_required")),
      model: yup.string().required(t("common.field_required")),
      plate: yup.string().required(t("common.field_required")),
      // last_km_update: yup.date(),
    })
    .required();

  const statusOption = useMemo(
    () => [
      { label: t("common.active"), value: 1 },
      { label: t("common.in_active"), value: 2 },
    ],
    [t, i18n.language]
  );

  const carStatusOption = useMemo(
    () => [
      { label: t("types.reason.new"), value: 1 },
      { label: t("cars.used"), value: 2 },
    ],
    [t, i18n.language]
  );

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    control,
    register,
    setError,
    watch,
    setValue,
  } = useForm({
    defaultValues: {
      name: "",
      model: "",
      plate: "",
      note: "",
      purchase_year: formatDate(today),
      inspection_expiry_date: formatDate(today),
      license_expiry_date: formatDate(today),
      purchase_km: 0,
      car_condition: {
        value: 1,
        label: t("types.reason.new"),
      },
      car_owner: "",
      last_km: 0,
      status: {
        value: 1,
        label: t("common.active"),
      },
      image: null,
    },
    resolver: yupResolver(schema),
  });

  // Reset form when language changes
  useEffect(() => {
    reset({
      name: "",
      model: "",
      plate: "",
      note: "",
      purchase_year: formatDate(today),
      inspection_expiry_date: formatDate(today),
      license_expiry_date: formatDate(today),
      purchase_km: 0,
      car_condition: {
        value: 1,
        label: t("types.reason.new"),
      },
      car_owner: "",
      last_km: 0,
      status: {
        value: 1,
        label: t("common.active"),
      },
      image: null,
    });
  }, [i18n.language, t, reset]);

  useEffect(() => {
    if (watch("purchase_km")) {
      setValue("last_km", watch("purchase_km"));
    }
  }, [watch("purchase_km")]);
  // const history = useHistory();

  const navigate = useNavigate();

  const handelCloseSideBar = () => {
    reset({
      name: "",
      model: "",
      plate: "",
      note: "",
      purchase_year: formatDate(today),
      license_expiry_date: formatDate(today),
      inspection_expiry_date: formatDate(today),
      purchase_km: null,
      last_km: null,
      car_condition: null,
      car_owner: "",
      // last_km_update: "",
      status: statusOption[0],
    });
    navigate(-1);
  };

  // Submit form
  const onSubmit = async (data) => {
    const now = new Date(Date.now());
    const formattedTime = now.toLocaleTimeString("en-GB", {
      hour12: false,
    });
    const formData = {
      ...data,
      status: data.status.value,
      license_expiry_date: data.license_expiry_date + " " + formattedTime,
      inspection_expiry_date: data.inspection_expiry_date + " " + formattedTime,
      car_condition: data.car_condition.value,
      // last_km_update: formatISOToCustomDate(data.last_km_update),
      // last_km_update: formatISOToCustomDate(data.),
    };

    // appendIfNotNull("name", data.name, formData);
    // appendIfNotNull("model", data.model, formData);
    // appendIfNotNull("plate", data.plate, formData);
    // appendIfNotNull("note", data.note, formData);
    // appendIfNotNull("purchase_year", data.purchase_year, formData);
    // appendIfNotNull("purchase_km", data.purchase_km, formData);
    // appendIfNotNull("last_km", data.last_km, formData);
    // appendIfNotNull("last_km_update", data.last_km_update, formData);
    // appendIfNotNull("status", data.status ? data.status : 1, formData);

    // console.log("data.status", formData);

    // if (data.image) appendIfNotNull("image", data.image[0], formData);
    try {
      const response = await USERSAPis.addUser({
        // name: data.name,
        // model: data.model,
        // plate: data.plate,
        // note: data.note,
        // purchase_year: data.purchase_year,
        // purchase_km: Number(data.purchase_km),
        // last_km: Number(data.last_km),
        // last_km_update: data.last_km_update,
        // status: Number(data.status),
        formData,
      });
      toastr.success(response.message);

      handelCloseSideBar();
    } catch (error) {
      // toastr.error("There are error ");
      // Object.keys(error.response.data.errors).forEach((field) => {
      //   setError(field, {
      //     type: "manual",
      //     message: error.response.data.errors[field][0],
      //   });
      // });
      // console.log("error", error);
      handleBackendErrors({ error, setError });
    }
    // Call API with selected permissions (data.permissions)
  };

  const UpdateFun = async (data) => {
    // const formData = new FormData();
    // // Append fields conditionally
    // const jsonData = {};

    // // Helper function to conditionally add fields
    // const appendIfNotNull = (key, value) => {
    //   if (value !== null && value !== undefined) {
    //     jsonData[key] = value;
    //   }
    // };

    // appendIfNotNull("name", data.name, formData);
    // appendIfNotNull("model", data.model, formData);
    // appendIfNotNull("plate", data.plate, formData);
    // appendIfNotNull("note", data.note, formData);
    // appendIfNotNull("purchase_year", data.purchase_year, formData);
    // appendIfNotNull("purchase_km", data.purchase_km, formData);
    // appendIfNotNull("last_km", data.last_km, formData);
    // appendIfNotNull("last_km_update", data.last_km_update, formData);
    // appendIfNotNull("status", data.status, formData);
    // appendIfNotNull("image", data.image[0], formData);
    const now = new Date(Date.now());
    const formattedTime = now.toLocaleTimeString("en-GB", {
      hour12: false,
    });
    const formData = {
      ...data,
      status: data.status.value,
      license_expiry_date: data.license_expiry_date + " " + formattedTime,
      inspection_expiry_date: data.inspection_expiry_date + " " + formattedTime,
      car_condition: data.car_condition.value,
      // last_km_update: formatISOToCustomDate(data.last_km_update),
    };

    try {
      const response = await USERSAPis.updateUser({
        // name: data.name,
        // model: data.model,
        // plate: data.plate,
        // note: data.note,
        // purchase_year: data.purchase_year,
        // purchase_km: Number(data.purchase_km),
        // last_km: Number(data.last_km),
        // last_km_update: data.last_km_update,
        // id: selectId,
        // status: Number(data.status),
        formData,
        id: Number(selectId),
      });
      toastr.success(response.message);
      handelCloseSideBar();
    } catch (error) {
      // toastr.error("There are error");
      console.log("error", error);
      handleBackendErrors({ error, setError });
    }
    // Call API with selected permissions (data.permissions)
  };

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoading && data?.result) {
      // Populate form with role data when loaded
      reset({
        name: data?.result.name || "",
        model: data?.result.model || "",
        plate: data?.result.plate || "",
        note: data?.result.note || "",
        purchase_year: data?.result.purchase_year,
        purchase_km: data?.result.purchase_km,
        last_km: data?.result.last_km,
        car_owner: data?.result.car_owner,
        inspection_expiry_date: data?.result.license_expiry_date?.split(" ")[0],
        license_expiry_date: data?.result.inspection_expiry_date?.split(" ")[0],
        car_condition: {
          value: data?.result.car_condition,
          label: carStatusOption.find(
            (item) => item.value === data?.result?.car_condition
          )?.label,
        },
        status: {
          value: data?.result.status,
          label: statusOption.find(
            (item) => item.value === data?.result?.status
          )?.label,
        },
        // last_km_update: data?.result.last_km_update,
      });
    }
  }, [selectId, isLoading, data?.result]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("common.cars")}
          titleOfSection={t("common.cars")}
          titleOfPage={
            isShow
              ? t("common.show")
              : selectId
              ? t("common.update")
              : t("common.create")
          }
          titleLink="/cars"
          currentPageLink={pathname}
        />
        <Card>
          <CardBody>
            <form
              onSubmit={
                selectId ? handleSubmit(UpdateFun) : handleSubmit(onSubmit)
              }
            >
              {isLoading ? (
                <div className="container-loading">
                  <ClipLoader color="#ddd" size={50} />
                </div>
              ) : (
                <Row className="g-2">
                  <Col xs={3}>
                    <div className="mb-2">
                      <CustomInput
                        name="name"
                        control={control}
                        isDisabled={isShow}
                        error={errors.name}
                        placeholder={t("cars.car_type")}
                      />
                    </div>
                  </Col>
                  <Col xs={3}>
                    <div className="mb-2">
                      <CustomInput
                        name="model"
                        control={control}
                        label={t("cars.modal")}
                        isDisabled={isShow}
                        error={errors.model}
                        placeholder={t("cars.modal")}
                      />
                    </div>
                  </Col>
                  <Col xs={3}>
                    <div className="mb-2">
                      <CustomInput
                        name="plate"
                        control={control}
                        label={t("cars.plate")}
                        isDisabled={isShow}
                        error={errors.plate}
                        placeholder={t("cars.plate")}
                      />
                    </div>
                  </Col>
                  <Col xs={3}>
                    <div className="">
                      <CustomInput
                        name="purchase_year"
                        control={control}
                        label={t("cars.purchase-year_input")}
                        type="date"
                        isDisabled={isShow}
                        error={errors.purchase_year}
                        placeholder={t("cars.purchase-year_input")}
                      />
                    </div>
                  </Col>

                  <Col xs={3}>
                    <div className="mb-2">
                      <CustomSelect
                        name="car_condition"
                        control={control}
                        label={t("cars.car_Status")}
                        menuHeight={80}
                        options={carStatusOption}
                        isDisabled={isShow}
                        error={errors.car_condition}
                      />
                    </div>
                  </Col>

                  <Col xs={3}>
                    <div className="mb-2">
                      <CustomInput
                        name="purchase_km"
                        control={control}
                        label={t("cars.purchase_km")}
                        type="number"
                        isDisabled={isShow}
                        error={errors.purchase_km}
                        placeholder={t("cars.purchase_km")}
                      />
                    </div>
                  </Col>

                  <Col xs={3}>
                    <div className="mb-2">
                      <CustomInput
                        name="last_km"
                        control={control}
                        label={t("common.last_km")}
                        type="number"
                        isDisabled={isShow}
                        error={errors.last_km}
                        placeholder={t("common.last_km")}
                      />
                    </div>
                  </Col>

                  <Col xs={3}>
                    <div className="">
                      <CustomInput
                        name="car_owner"
                        control={control}
                        type="text"
                        isDisabled={isShow}
                        error={errors.purchase_year}
                        placeholder={t("cars.car_owner")}
                      />
                    </div>
                  </Col>

                  <Col xs={3}>
                    <div className="">
                      <CustomInput
                        name="inspection_expiry_date"
                        control={control}
                        type="date"
                        isDisabled={isShow}
                        error={errors.inspection_expiry_date}
                        placeholder={t("cars.car_card_expire")}
                      />
                    </div>
                  </Col>

                  <Col xs={3}>
                    <div className="">
                      <CustomInput
                        name="license_expiry_date"
                        control={control}
                        type="date"
                        isDisabled={isShow}
                        error={errors.license_expiry_date}
                        placeholder={t("cars.examination_expiration_date")}
                      />
                    </div>
                  </Col>
                  {/* <Col xs={3}>
                    <div className="mb-2">
                      <Label className="form-label" htmlFor="last_km_update">
                        Last km update
                      </Label>
                      <input
                        type="text"
                        onFocus={(e) => (e.target.type = "date")} // Changes to date on focus
                        onBlur={(e) => (e.target.type = "text")} // Reverts to text on blur
                        placeholder="Last update" // Shows placeholder
                        disabled={isShow}
                        className={`form-control ${
                          errors.last_km_update ? "is-invalid" : ""
                        }`}
                        id="last-update-input"
                        {...register("last_km_update")}
                      />
                      {errors.last_km_update && (
                        <div className="invalid-feedback">
                          {errors.last_km_update.message}
                        </div>
                      )}
                    </div>
                  </Col> */}

                  <Col xs={3}>
                    <div className="mb-2">
                      <CustomSelect
                        name="status"
                        control={control}
                        label={t("common.status")}
                        options={statusOption}
                        isDisabled={isShow}
                        error={errors.status}
                      />
                    </div>
                  </Col>
                  {/* <Col xs={3}>
                    <div
                      className="mb-2 position-relative"
                      style={{ top: "-20px" }}
                    >
                      <Label className="form-label" htmlFor="file">
                        {t("common.select_image")}
                      </Label>
                      <input
                        type="file"
                        disabled={isShow}
                        className="form-control"
                        id="customFile"
                        {...register("image")}
                      />
                    </div>
                  </Col> */}
                  <Col xs={12}>
                    <div className="mb-2">
                      {/* <Label className="form-label" htmlFor="Note">
                        {t("common.note")}
                      </Label> */}
                      <CustomTextArea
                        name="note"
                        control={control}
                        isShow={isShow}
                        placeholder={t("common.note")}
                        rows={8}
                        error={errors.note}
                      />
                    </div>
                  </Col>
                  <div
                    style={{ display: "flex", alignItems: "center", gap: 8 }}
                  >
                    <Button
                      type="button"
                      color="light"
                      onClick={() => navigate(-1)}
                      className="btn-sm "
                      style={{ height: "32px", width: "54px" }}
                    >
                      {t("common.close")}
                    </Button>
                    {!isShow && (
                      <Button
                        color="primary"
                        className="btn-sm waves-effect waves-light primary-button"
                        type="submit"
                      >
                        {isSubmitting ? (
                          <ClipLoader color="white" size={15} />
                        ) : selectId ? (
                          t("common.update")
                        ) : (
                          t("common.add")
                        )}
                      </Button>
                    )}
                  </div>
                </Row>
              )}
            </form>
          </CardBody>
        </Card>
      </Container>
    </div>
  );
};
export default CarsActions;
