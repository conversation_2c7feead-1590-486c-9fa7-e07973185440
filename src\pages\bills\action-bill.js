import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  <PERSON><PERSON>,
  Col,
  Container,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON>,
} from "reactstrap";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useMemo, useRef, useState } from "react";
import toastr from "toastr";
import { useLocation, useNavigate } from "react-router-dom";
import { billAPis } from "../../apis/bills/api";
import { clientsQueries } from "../../apis/clients/query";
import { billTypesQueries } from "../../apis/types/bill-type/query";
import { useForm, useWatch } from "react-hook-form";
import { BillQueries } from "../../apis/bills/query";
import { ContracyQueries } from "../../apis/contract/query";
import {
  formatDate,
  handleBackendErrors,
  today,
} from "../../helpers/api_helper";
import { vatQueries } from "../../apis/vat/query";
import { productQueries } from "../../apis/products/query";
import TableContainer from "../../components/Common/TableContainer";
import { useTranslation } from "react-i18next";
import { delegateQueries } from "../../apis/delegate/query";
import useSetSelectOptions from "../../hooks/use-set-select-options";
import ActionSection from "../../components/Common/ActionSection";
import CustomSelect from "../../components/Common/Select";

import Input from "../../components/Common/Input";
import TextAreaField from "../../components/Common/textArea";

// Form Fields Components
export const FormField = ({
  field,
  errors,
  isShow,
  control,
  placeholder,
  cols = 2,
  isDisabled,
  onTotalChange,
}) => (
  <Col xs={cols} key={field.id}>
    <Input
      control={control}
      error={errors[field.name]}
      min={
        field.type === "date"
          ? formatDate(
              new Date(new Date().setFullYear(new Date().getFullYear() - 2))
            )
          : undefined
      }
      isDisabled={isDisabled || field.disable || isShow}
      step={field.type === "number" ? "any" : undefined}
      placeholder={placeholder}
      type={field.type}
      name={field.name}
      onChange={field.name === "total" ? onTotalChange : undefined}
    />
  </Col>
);

// Product Modal Components
const ProductModal = ({ isOpen, onClose, onAdd, children, isEditMode }) => {
  const { t } = useTranslation();

  const handleClose = () => {
    onClose();
  };

  return (
    <Modal isOpen={isOpen} backdrop="static">
      <ModalHeader toggle={handleClose}>
        {isEditMode ? t("common.update") : t("common.add")}{" "}
        {t("common.product")}
      </ModalHeader>
      <ModalBody>
        <Row className="g-1">{children}</Row>
        <ModalFooter>
          <Button
            type="button"
            color="light"
            className="btn-sm"
            onClick={handleClose}
          >
            {t("common.close")}
          </Button>
          <Button
            type="button"
            className="btn-sm"
            onClick={onAdd}
            color="primary"
          >
            {isEditMode ? t("common.update") : t("common.add")}
          </Button>
        </ModalFooter>
      </ModalBody>
    </Modal>
  );
};

const BillsActions = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const selectId = Number(queryParams.get("id")?.split("?")[0]);
  const isShow = queryParams.get("id")?.split("?")[1];
  const [typeFromUrl, setTypeFromUrl] = useState(
    Number(queryParams.get("type"))
  );
  const [openAddModal, setOpenAddModal] = useState(false);
  const [optionContract, setOptionContract] = useState([]); // Assuming you have a way to populate this
  const [productList, setProductList] = useState([]);
  const [selectedProductId, setSelectedProductId] = useState(0);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isOpenEditModel, setIsOpenEditModel] = useState(false);
  const [isResting, setIsResting] = useState(false);
  const [initialDataLoaded, setInitialDataLoaded] = useState(false);
  const [selectedTotalPrice, setSelectedTotalPrice] = useState(0);
  const [totalBeforeTax, setTotalBeforeTax] = useState(0);
  const [selectedQuant, setSelectedQuant] = useState(0);
  const [selctedProductType, setSelectedProductType] = useState([]);
  const [selectedBills, setSelectedBills] = useState();
  const [checkPriceOnce, setCheckPriceOnce] = useState(false);
  const [selectedBillType, setSelectedBillType] = useState(0);
  const { t, i18n } = useTranslation();
  const [billTypeId, setBillTypeId] = useState();

  const { data, isLoading, isRefetching } = BillQueries.useGetBill({
    id: Number(selectId),
  });

  const { data: clients } = clientsQueries.useGetAll({ status: 1 });

  const { data: vat } = vatQueries.useGetAll({
    enable:
      typeFromUrl === 7 ||
      typeFromUrl === 4 ||
      typeFromUrl === 6 ||
      typeFromUrl === 2,
  });

  const { data: bilTypes } = billTypesQueries.useGetAll({});

  const { data: delegates } = delegateQueries.useGetAll({ status: 1 });

  const { data: bill_numbers } = BillQueries.useGetGenerateBill({
    id: typeFromUrl,
    enabled: !selectId,
  });

  const navigate = useNavigate();

  useEffect(() => {
    if (bilTypes?.result) {
      const selectedBillType = bilTypes?.result.find(
        (item) => item.type === typeFromUrl
      );
      setSelectedBillType(selectedBillType?.id);
    }
  }, [bilTypes?.result]);

  const handelCancel = () => {
    navigate(-1);
    reset();
  };

  const schema = yup
    .object({
      client_id: yup
        .object()
        .shape({
          label: yup.string().required(),
          value: yup.number().required(),
        })
        .required(t("common.field_required")),
      bill_date: yup.string().required(t("common.field_required")),
      contract_id:
        typeFromUrl === 7
          ? yup
              .mixed()
              .test(
                "is-required-for-contract-bill",
                t("common.field_required"),
                function (value) {
                  const { bill_type_id } = this.parent;

                  // If bill_type_id is 7, contract_id must be an object with a non-empty `value`
                  if (bill_type_id?.value === 7) {
                    return (
                      value &&
                      typeof value === "object" &&
                      "value" in value &&
                      value.value !== null &&
                      value.value !== ""
                    );
                  }

                  // Otherwise, validation passes
                  return true;
                }
              )
          : "",
      // total: yup
      //   .number()
      //   .min(1, t("bills.validation.greater_than_0"))
      //   .max(1000000, t("cars.validations.km_max"))
      //   .nullable(),
    })
    .required();

  const initialSTate = useMemo(
    () => ({
      bill_type_id: typeFromUrl,
      client_id: null,
      pm_bill_id: null,
      contract_id: null,
      delegate_id: null,
      bill_date: formatDate(today),
      return_date: formatDate(today),
      notes: "",
      res_name: "",
      total: null,
      discount_percentage: null,
      discount_value: null,
      change_oil: null,
      quant: null,
      product_id: null,
      product_note: "",
      product_gift: null,
      product_total: null,
      product_price: null,
      discount_product: null,
      discount_product_type: "",
      total_discounts: null,
      total_discount_type: "",
      total_discount_percentage: null,
      total_discount_offer: null,
      bill_number: null,
      vat_rate: 0,
    }),
    []
  );

  const type = [
    "",
    "Sample Bill",
    "Return Bill",
    "PickupMaintenance Bill",
    "DeliverMaintenance Bill",
    "ChangeOil Bill",
    "Sales Bill",
    "Contract Bill",
  ];

  const typesToShow = [
    { label: t("types.bill.sales_bill"), value: 6 },
    { label: t("types.bill.return_bill"), value: 2 },
    { label: t("types.bill.change_oil"), value: 5 },
    { label: t("types.bill.contract_bill"), value: 7 },
    { label: t("types.bill.pickup_maintenance_bill"), value: 3 },
    { label: t("types.bill.deliver_maintenance_bill"), value: 4 },
    { label: t("types.bill.sample_bill"), value: 1 },
    { label: t("types.bill.return_sample_bill"), value: 9 },
  ];

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    register,
    setError,
    control,
    watch,
    clearErrors,
    setValue,
  } = useForm({
    defaultValues: { ...initialSTate },
    resolver: yupResolver(schema),
    mode: "onBlur",
  });

  const { data: pickUpList } = BillQueries.useGetAllPickUpData({
    client_id: watch("client_id")?.value,
    enabled: typeFromUrl === 4 && watch("client_id")?.value > 0,
  });

  const { data: Contract } = ContracyQueries.useGetAllListBillsForDelete({
    id: watch("client_id")?.value,
    enable: watch("client_id")?.value > 0,
  });

  const handelOpenAddModal = () => {
    setIsEditMode(false);
    setSelectedProductId(0);
    setOpenAddModal(true);
    // Clear any existing errors when opening modal
    clearErrors();
  };

  const resetInputs = () => {
    setValue("product_id", null);
    setValue("quant", null);
    setValue("product_note", "");
    setValue("maintaince_quant", null);
    setValue("change_oil", null);
    setValue("product_gift", null);
    setValue("product_total", null);
    setValue("return_date", formatDate(today));
    setValue("product_price", null);
    setValue("discount_product", null);
    setValue("discount_product_type", "");
    setValue("pm_bill_id", null);
    setValue("product_discount_value", null);
    setValue("product_discount_percentage", null);
    setValue("bill_number", null);
    // setValue("total_discount_value", null);
    // setValue("total_discount_percentage", null);
    setSelectedProductId(0);
    setIsEditMode(false);
    setValue("total_discounts", null);
    setValue("total_discount_type", "");
    setValue("total_discount_offer", null);
  };

  const handelCloseAddModal = () => {
    setOpenAddModal(false);
    resetInputs();
    // Clear any existing errors when closing modal
    clearErrors();

    // Explicitly reset these values
    setSelectedProductId(0);
    setIsEditMode(false);
    setSelectedQuant(null);
  };

  const { data: products } = productQueries.useGetAll({
    status: 1,
    billType: selectedBillType,
  });

  useEffect(() => {
    if (
      watch("total_discount_type") === "percentage" &&
      Number(watch("total_discount_offer")) > 100
    ) {
      setError("total_discount_offer", {
        type: "manual",
        message: t("bills.max_percentage_value"),
      });
    }
  }, [watch("total_discount_offer")]);

  useEffect(() => {
    if (
      watch("discount_product_type") === "percentage" &&
      Number(watch("discount_product")) > 100
    ) {
      setError("discount_product", {
        type: "manual",
        message: t("bills.max_percentage_value"),
      });
    }
  }, [watch("discount_product")]);

  useEffect(() => {
    if (watch("product_price") && !isOpenEditModel) {
      setValue(
        "product_total",
        Number(watch("product_price")) * Number(watch("quant"))
      );
    }
  }, [watch("product_price"), watch("quant")]);

  const fieldsNames = [
    {
      name: "bill_date",
      isRequired: false,
      errorMessage: errors?.bill_date?.message,
      label: t("bills.bill_date"),
      type: "date",
      placeholder: "",
      showIn: true,
    },
    {
      name: "res_name",
      isRequired: false,
      errorMessage: errors?.res_name?.message,
      label: t("common.responsible"),
      type: "text",
      showIn: true,
      disabled: isShow,
    },
    {
      name: "return_date",
      isRequired: false,
      errorMessage: errors?.return_date?.message,
      label: t("bills.return_bill"),
      type: "date",
      showIn: typeFromUrl === 3, // PickupMaintenance Bill
    },
  ];

  const totalsFields = [
    {
      name: "total",
      isRequired: false,
      errorMessage: errors?.total?.message,
      label:
        typeFromUrl === 3
          ? t("bills.pickup_maintenance_price")
          : typeFromUrl === 6
          ? t("common.rows_total")
          : t("common.total"),
      type: "number",
      disable:
        typeFromUrl === 7 || typeFromUrl === 3 || typeFromUrl === 2
          ? false
          : true,
      showIn: [6, 7, 3, 2].includes(typeFromUrl), // Sales, Return, Contract Bills
    },
    {
      name: "total_discounts",
      isRequired: false,
      errorMessage: errors?.total_discounts?.message,
      label: t("common.rows_discount"),
      type: "number",
      showIn: typeFromUrl === 6, // Sales Bill
      disabled: true,
    },
  ];

  const textAreaField = [
    {
      id: 0,
      name: "note",
      label: t("common.note"),
      isRequired: false,
      showIn: true,
    },
  ];

  useEffect(() => {
    if (Contract?.result?.length > 0) {
      setOptionContract(
        Contract.result.map((item) => ({
          label: item.contract_number,
          value: item.id,
        }))
      );
    }
  }, [Contract?.result]);

  const changeOilOptions = [
    { label: t("common.restore"), value: 0 },
    { label: t("common.add"), value: 1 },
  ];

  const productTypesOption = useSetSelectOptions({
    data: selctedProductType,
    getOption: (item) => ({
      label: i18n.language === "eng" ? item.title.en : item.title.ar, // Dynamic label property
      value: item.id, // Assuming there's an id
    }),
  });

  const clientOptions = useSetSelectOptions({
    data: clients?.result,
    getOption: (item) => ({
      label: `${item.company_name}/${item.full_name || "---"}`,
      value: item.id,
    }),
  });

  const productsOptions = useSetSelectOptions({
    data: products?.result,
    getOption: (item) => ({
      label: item.name,
      value: item.id,
    }),
  });

  const productsFields = [
    // {
    //   id: -1,
    //   field: (
    //     <Col xs={4} className="mb-2">
    //       <CustomSelect
    //         control={control}
    //         name="ProductTypeId"
    //         isClearable
    //         isDisabled={
    //           isShow || watch("pm_bill_id")?.value || typeFromUrl === 4
    //         }
    //         placeholder={t("types.product_types.product_type")}
    //         options={productTypesOption}
    //         error={errors?.ProductTypeId}
    //       />
    //     </Col>
    //   ),
    //   showIn: true,
    // },
    {
      id: 2,
      field: (
        <Col xs={typeFromUrl === 1 ? 6 : 4}>
          <CustomSelect
            control={control}
            name="change_oil"
            isClearable
            isDisabled={isShow}
            placeholder={t("bills.change_oil_bill")}
            options={changeOilOptions}
            error={errors?.change_oil}
          />
        </Col>
      ),
      showIn: typeFromUrl === 5, // ChangeOil Bill
    },
    {
      id: 0,
      field: (
        <Col xs={typeFromUrl === 1 ? 6 : 4} className="mb-2">
          <CustomSelect
            control={control}
            name="product_id"
            placeholder={t("common.product")}
            isDisabled={
              isShow || watch("pm_bill_id")?.value || typeFromUrl === 4
            }
            options={productsOptions.filter(
              (item) =>
                !productList.some((val) => val.product_id === item.value)
            )}
            error={errors?.product_id}
          />
        </Col>
      ),
      showIn: true,
    },
    {
      id: 1,
      field: (
        <Col xs={typeFromUrl === 1 ? 6 : 4}>
          <Input
            control={control}
            name="quant"
            isDisabled={!watch("product_id") || isShow}
            placeholder={t("common.quant")}
            type="number"
            min={1}
            max={5000}
            error={errors?.quant}
          />
        </Col>
      ),
      showIn: [1, 5, 6, 7, 2].includes(typeFromUrl),
    },
    {
      id: 4,
      field: (
        <Col xs={4}>
          <Input
            control={control}
            name="product_price"
            isDisabled={isShow}
            placeholder={t("common.price")}
            type="number"
            error={errors?.product_price}
          />
        </Col>
      ),
      showIn: [6, 2].includes(typeFromUrl), // Sales, Return Bills
    },
    {
      id: 5,
      field: (
        <Col xs={4}>
          <Input
            control={control}
            name="product_gift"
            isDisabled={isShow}
            placeholder={t("common.gift")}
            type="number"
            error={errors?.product_gift}
          />
        </Col>
      ),
      showIn: typeFromUrl === 6, // Sales Bill
    },
    {
      id: 6,
      field: (
        <Col xs={4}>
          <Input
            control={control}
            name="product_total"
            isDisabled={isShow}
            placeholder={t("common.total")}
            type="number"
            error={errors?.product_total}
          />
        </Col>
      ),
      showIn: [6, 2].includes(typeFromUrl), // Sales, Return Bills
    },
  ];

  const handelFilterFromProductList = (id) => {
    // Filter out the item with the given id from both productList and rowData
    const updatedProductList = productList.filter(
      (item) => item.product_id !== id
    );

    // Update the productList state (this will automatically trigger a re-render)
    setProductList(updatedProductList);

    // Recalculate totals after removing a product
    setTimeout(() => {
      recalculateTotals();
    }, 0);
  };

  const recalculateTotals = ({ totalValue, hasValue, vat_rate } = {}) => {
    if (hasValue) {
      setSelectedTotalPrice(Number(totalValue) || 0);
    } else if (productList.length > 0) {
      // Calculate total of all products
      let total = 0;
      let discounts = 0;
      productList.forEach((item) => {
        total += Number(item.total) || 0;
        discounts += Number(item.discount_value) || 0;
      });

      // Get the current manually set total, if any
      const currentTotal = hasValue ? totalValue : watch("total");
      const rowTotalDiscount = watch("total_discounts");

      // Check if we should allow manual total override
      // For bill types 3 (PickupMaintenance), 4 (DeliverMaintenance), 6 (Sales Bill), and 7 (Contract Bill)
      const allowManualOverride = [3, 4, 6, 7].includes(typeFromUrl);

      // If manual override is allowed and a value exists, use it
      if (
        allowManualOverride &&
        currentTotal !== null &&
        currentTotal !== undefined &&
        currentTotal !== ""
      ) {
        // Use the manually set total for further calculations
        total = Number(currentTotal) - Number(rowTotalDiscount);
      } else {
        // Otherwise set the calculated total
        // setValue("total", total);
      }
      setValue("total_discounts", discounts);

      // Get current discount values
      const discountValue = Number(watch("total_discount_value")) || 0;

      // Calculate the final total price after discounts but before tax
      let totalAfterDiscounts = total;

      // Apply discount if any
      if (discountValue > 0) {
        totalAfterDiscounts -= discountValue;
      }

      // Ensure totalAfterDiscounts is not negative
      totalAfterDiscounts = Math.max(totalAfterDiscounts, 0);

      // Set total before tax value
      setTotalBeforeTax(totalAfterDiscounts);
      setValue("total_before_tax", totalAfterDiscounts.toFixed(2));

      // Get tax rate from form
      const taxRate = hasValue ? vat_rate : Number(watch("vat_rate")) || 0;
      // Calculate the final total price with tax included
      let finalTotal = totalAfterDiscounts;
      if (taxRate > 0 && vat?.data?.vat_status === 1) {
        // Add tax to the total: finalTotal = totalBeforeTax + (totalBeforeTax * taxRate/100)
        const taxAmount = (totalAfterDiscounts * taxRate) / 100;
        finalTotal = totalAfterDiscounts + taxAmount;
      }

      // Update the selectedTotalPrice state (which is the total after tax)
      const finalTotalValue = Math.max(finalTotal, 0);
      if (typeFromUrl !== 7) {
        setSelectedTotalPrice(finalTotalValue);
      }

      // Store the calculated total in a hidden form field to persist it
      setValue("calculated_total_with_tax", finalTotalValue);

      // Force update the selectedTotalPrice in the form data to ensure it's available for submission
      if (typeFromUrl !== 3) {
        setValue("selectedTotalPrice", finalTotalValue);
      }

      // Log the calculation for debugging
    }
  };

  const handelAddProductToList = () => {
    try {
      // Capture all form values upfront to avoid stale closures
      const productId = watch("product_id");

      const quant = Number(watch("quant"));

      clearErrors();
      // Validate quantity for relevant bill types
      if ([1, 5, 6, 7, 2].includes(typeFromUrl)) {
        if (quant <= 0 && Number(watch("product_gift")) === 0) {
          setError("quant", {
            message: t("bills.validation.greater_than_0"),
          });
          return;
        }
      }

      if (watch("product_price") && Number(watch("product_price")) <= 0) {
        setError("product_price", {
          type: "manual",
          message: t("bills.validation.greater_than_0"),
        });
        return;
      }

      if (Number(watch("quant")) <= 0 && !Number(watch("product_gift"))) {
        setError("quant", {
          type: "manual",
          message: t("bills.validation.greater_than_0"),
        });
        return;
      }

      if (Number(watch("quant")) > 5000) {
        console.log('Number(watch("quant")),sas', Number(watch("quant")));
        setError("quant", {
          type: "manual",
          message: t("bills.validation.greater_than_5000"),
        });
        return;
      }

      if (watch("product_gift") && Number(watch("product_gift")) < 0) {
        setError("product_gift", {
          type: "manual",
          message: t("bills.validation.greater_than_0"),
        });
        return;
      }

      if (watch("product_gift") && Number(watch("product_gift")) > 5000) {
        setError("product_gift", {
          type: "manual",
          message: t("bills.validation.greater_than_5000"),
        });
        return;
      }

      if (watch("product_total") && Number(watch("product_total")) <= 0) {
        setError("product_total", {
          type: "manual",
          message: t("bills.validation.greater_than_0"),
        });
        return;
      }

      if (!productId) {
        setError("product_id", {
          type: "manual",
          message: t("common.field_required"),
        });
        return;
      }

      if (productId) {
        // Extract all form values
        const productNote = watch("product_note");
        const changeOil = watch("change_oil");
        const productGift = Number(watch("product_gift"));
        const productTotal = watch("product_total") ?? 0;
        const productPrice = Number(watch("product_price"));
        const discountValue = Number(watch("product_discount_value")) || 0;
        const discountPercentage =
          Number(watch("product_discount_percentage")) || 0;
        const ProductTypeId = watch("ProductTypeId");

        if (typeFromUrl === 5 && !changeOil) {
          setError("change_oil", {
            type: "manual",
            message: t("common.field_required"),
          });
          return;
        }

        // Validate discount vs total
        if (discountValue && productTotal < discountValue) {
          setError("product_discount_value", {
            type: "manual",
            message: t("bills.discount_validation"),
          });
          return;
        }

        if (0 > discountValue) {
          setError("product_discount_value", {
            type: "manual",
            message: t("bills.validation.greater_than_0"),
          });
          return;
        }

        if (0 > discountPercentage) {
          setError("product_discount_percentage", {
            type: "manual",
            message: t("bills.validation.greater_than_0"),
          });
          return;
        }

        if (discountPercentage && discountPercentage > 100) {
          setError("product_discount_percentage", {
            type: "manual",
            message: t("bills.validation.max_percentage_value"),
          });
          return;
        }

        // Calculate net price based on the discount
        const netPrice =
          discountValue > 0
            ? Number(productTotal) - discountValue
            : Number(productTotal);

        // Create new product object
        const newProduct = {
          product_id: productId.value,
          product_name: productId.label,
          quant: quant,
          notes: productNote,
          change_oil: changeOil?.value,
          gift: productGift ?? 0,
          total: productTotal,
          price: productPrice ?? 0,
          ProductTypeId: ProductTypeId?.value,
          net_price: netPrice,
          discount_value: discountValue,
          discount_percentage: discountPercentage,
          // Add a discount field for backward compatibility
          discount: discountValue,
        };

        if (isEditMode && selectedProductId) {
          // In edit mode, directly modify the productList
          const newProductList = [...productList];

          // Find the product in the list by matching product_id with selectedProductId
          const indexToUpdate = newProductList.findIndex(
            (item) => Number(item.product_id) === Number(selectedProductId)
          );

          if (indexToUpdate !== -1) {
            // Update the existing product
            newProductList[indexToUpdate] = newProduct;
            setProductList(newProductList);
          } else {
            // Fallback: add as new if not found (shouldn't happen)

            setProductList([...productList, newProduct]);
          }
        } else {
          // In add mode, simply append to the list
          setProductList([...productList, newProduct]);
        }

        setValue("product_id", null);
        setValue("quant", "");
        setValue("product_note", "");
        setValue("maintaince_quant", "");
        setValue("change_oil", "");
        setValue("product_gift", "");
        setValue("product_total", "");
        setValue("return_date", formatDate(today));
        setValue("product_price", "");
        setValue("discount_product", "");
        setValue("discount_product_type", "");
        setValue("product_discount_value", "");
        setValue("product_discount_percentage", "");
        if (isEditMode) setOpenAddModal(false);
        setSelectedQuant(0);
        setSelectedProductId(0);
        setIsEditMode(false);
        setCheckPriceOnce(false);

        // Calculate the new total directly
        let newTotal = 0;
        const updatedProductList = isEditMode
          ? productList.map((p) =>
              p.product_id === selectedProductId ? newProduct : p
            )
          : [...productList, newProduct];

        updatedProductList.forEach((item) => {
          newTotal += Number(item.total) || 0;
        });
        if (typeFromUrl === 6 || typeFromUrl === 2) {
          setValue("total", newTotal);
        }
        // Set the total value directly

        // Force recalculation of totals with a slight delay to ensure state is updated
        setTimeout(() => {
          // recalculateTotals();
          // Make sure the calculated_total_with_tax field is updated with the latest selectedTotalPrice
          const finalTotalValue =
            watch("calculated_total_with_tax") || selectedTotalPrice || 0;

          // Force update the form field to ensure it's available for submission
          if (finalTotalValue > 0) {
            setValue("selectedTotalPrice", finalTotalValue);
          }
        }, 100);
      }
    } catch (error) {
      console.log("Error handling product:", error);
    }
  };

  useEffect(() => {
    if (isOpenEditModel) {
      setTimeout(() => {
        setIsOpenEditModel(false);
      }, 1000);
    }
  }, [isOpenEditModel]);

  const handelSetUpdate = (id) => {
    // Clear any existing errors first
    clearErrors();

    // Find the product in the list
    const currentProduct = productList.find((item) => item.product_id === id);

    if (!currentProduct) {
      console.error("Product not found in list:", id);
      return;
    }

    // Set the selected product ID *before* opening the modal
    setSelectedProductId(id);
    setIsEditMode(true);

    // Open the modal after setting the state
    setOpenAddModal(true);

    // Set values with a slight delay to ensure form is ready
    setTimeout(() => {
      setValue("product_id", {
        label: currentProduct.product_name,
        value: currentProduct.product_id,
      });
      setValue("quant", currentProduct.quant);
      setValue("product_note", currentProduct.notes);
      setValue("product_gift", currentProduct.gift);
      setValue("product_total", Number(currentProduct.total));
      setValue("maintaince_quant", currentProduct.maintaince_quant);
      setValue("product_price", currentProduct.price);
      setValue(
        "product_discount_percentage",
        currentProduct.discount_percentage
      );
      setValue("product_discount_value", currentProduct.discount_value);
      setValue("change_oil", {
        label: changeOilOptions[currentProduct.change_oil]?.label,
        value: changeOilOptions[currentProduct.change_oil]?.value,
      });
      setValue("product_discount_value", currentProduct.discount_value);
      setValue(
        "product_discount_percentage",
        currentProduct.discount_percentage
      );
      const SelectedProductType = {
        label: productTypesOption.find(
          (opt) => opt.value === currentProduct.ProductTypeId
        )?.label,
        value: currentProduct.ProductTypeId,
      };

      if (currentProduct.ProductTypeId) {
        setValue("ProductTypeId", SelectedProductType);
      }

      // Log the current state after setting values
    }, 100);
  };

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoading && data?.result) {
      const returnedProduct = [];
      setIsResting(true);
      setInitialDataLoaded(false); // Reset this flag when loading new data
      // Update typeFromUrl with bill_type_id from backend when editing
      if (data?.result.bill_type && data?.result.bill_type.id) {
        setTypeFromUrl(data?.result.bill_type.type);
        setBillTypeId(data?.result.bill_type.id);
      }

      data?.result.details?.map((item) => {
        returnedProduct.push({
          product_id: item.product.id,
          product_name: item.product.name,
          quant: item.quant,
          notes: item.notes,
          change_oil: item.change_oil,
          gift: item.gift,
          total: item.total,
          maintaince_quant: item.maintaince_quant,
          price: item.price,
          discount_value: item.discount_value || 0,
          discount_percentage: item.discount_percentage || 0,
          net_price:
            item.discount_value > 0
              ? item.total - item.discount_value
              : item.total - (item.total * item.discount_percentage) / 100,
          discount: item.discount_value || 0,
          ProductTypeId: item?.product?.product_type?.id,
        });
      });
      setProductList(returnedProduct);

      // Calculate total_discounts from products
      let totalProductDiscounts = 0;
      data?.result.details?.forEach((item) => {
        totalProductDiscounts += Number(item.discount_value || 0);
      });

      setSelectedTotalPrice(Number(data?.result.total) || 0);

      const taxAmount = data?.result.vat_rate / 100;
      const original_value = data?.result.total / (1 + taxAmount);

      // Populate form with role data when loaded
      reset({
        client_id: {
          label: `${data?.result.client.company_name}/${
            data?.result.client.full_name || "---"
          }`,
          value: data?.result.client.id,
        },
        bill_date: data?.result.bill_date?.split(" ")[0],
        res_name: data?.result.res_name || "---",
        delegate_id: {
          label: data?.result.delegate?.full_name,
          value: data?.result.delegate?.id,
        },
        total: original_value - totalProductDiscounts,
        total_discounts:
          totalProductDiscounts || data?.result.products_discount || 0,
        total_discount_value: data?.result.discount_value || 0,
        total_discount_percentage: data?.result.discount_percentage || 0,
        vat_rate: data?.result.vat_rate,
        return_date: data?.result.return_date?.split(" ")[0],
        notes: data?.result.notes,
        discount_percentage: data?.result.discount_percentage,
        discount_value: data?.result.discount_value,
        contract_id: data?.result?.contract_id
          ? {
              label: data?.result?.contract_id?.contract_number,
              value: data?.result.contract_id?.id,
            }
          : null,
        bill_number: {
          label: data?.result?.bill_number,
          value: data?.result.id,
        },
      });

      // Make sure to recalculate totals after loading data
      setTimeout(() => {
        // if (typeFromUrl === 2 || typeFromUrl === 6) {
        recalculateTotals({
          totalValue: data?.result.total,
          hasValue: true,
          vat_rate: data?.result.vat_rate,
        });
        // }
      }, 100);

      setTimeout(() => {
        setIsResting(false);
        setInitialDataLoaded(true); // Set this flag when data loading is complete
      }, 1000);
    }
  }, [data?.result, selectId]);

  useEffect(() => {
    if (watch("bill_number")?.value) {
      const value = pickUpList?.result.find(
        (item) => item.id === watch("bill_number")?.value
      );

      setSelectedBills(value);
    }
  }, [watch("bill_number")?.value]);

  useEffect(() => {
    if (bilTypes?.result?.length > 0) {
      const foundItem = bilTypes.result.find(
        (item) => item.type === Number(typeFromUrl)
      );
      setBillTypeId(foundItem?.id);
    }
  }, [bilTypes?.result]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  // Convert data to FormData and send it
  const UpdateFun = async (_data) => {
    if (productList.length === 0) {
      toastr.error(t("bills.validation.select_product_validation"));
      window.scrollTo(0, document.body.scrollHeight);
      return;
    }
    const isAddChangeOil = productList.find((item) => item?.change_oil === 1);
    const isRestoreChangeOil = productList.find(
      (item) => item?.change_oil === 0
    );

    if (typeFromUrl === 5 && (!isRestoreChangeOil || !isAddChangeOil)) {
      toastr.error(t("bills.validation.change_oli_val"));
      return;
    }

    try {
      // Calculate total discounts from products
      let totalProductDiscounts = 0;
      productList.forEach((item) => {
        totalProductDiscounts += Number(item.discount_value || 0);
      });
      // Ensure the discount values are the latest
      // recalculateTotals();

      const discountValue = Number(watch("total_discount_value")) || 0;
      const discountPercentage =
        Number(watch("total_discount_percentage")) || 0;
      const taxRate = Number(watch("vat_rate")) || 0;

      const dataToSend = {
        products: productList,
        contract_id: _data.contract_id?.value,
        client_id: _data.client_id?.value,
        bill_date: _data.bill_date,
        bill_type_id: data?.result?.bill_type.id,
        return_date: _data.return_date,
        notes: _data.notes,
        delegate_id: _data?.delegate_id?.value || null,
        total:
          typeFromUrl === 3
            ? Number(_data.total)
            : watch("calculated_total_with_tax") || selectedTotalPrice || 0,
        total_before_tax: totalBeforeTax ?? 0,
        products_discount: totalProductDiscounts,
        vat_rate: taxRate,
        discount_value: discountValue,
        discount_percentage: discountPercentage,
        res_name: _data.res_name,
        // pm_bill_id: _data.bill_number?.value,
      };

      const response = await billAPis.update({
        payload: dataToSend,
        id: selectId,
      });
      toastr.success(response.message);
      navigate(-1);
      reset();
    } catch (error) {
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
  };

  const addFun = async (data) => {
    if (productList.length === 0) {
      toastr.error(t("bills.validation.select_product_validation"));
      window.scrollTo(0, document.body.scrollHeight);
      return;
    }
    const isAddChangeOil = productList.find((item) => item?.change_oil === 1);
    const isRestoreChangeOil = productList.find(
      (item) => item?.change_oil === 0
    );

    if (typeFromUrl === 5 && (!isRestoreChangeOil || !isAddChangeOil)) {
      toastr.error(t("bills.validation.change_oli_val"));
      return;
    }
    try {
      // Calculate total discounts from products
      let totalProductDiscounts = 0;
      productList.forEach((item) => {
        totalProductDiscounts += Number(item.discount_value || 0);
      });

      // Ensure the discount values are the latest
      // recalculateTotals();

      const discountValue = Number(watch("total_discount_value")) || 0;
      const discountPercentage =
        Number(watch("total_discount_percentage")) || 0;
      const taxRate = Number(watch("vat_rate")) || 0;

      const dataToSend = {
        products: productList,
        contract_id: data.contract_id?.value,
        client_id: data.client_id?.value,
        bill_date: data.bill_date,
        bill_type_id: billTypeId,
        return_date: data.return_date,
        notes: data.notes,
        total:
          typeFromUrl === 3
            ? Number(data.total)
            : watch("calculated_total_with_tax") || selectedTotalPrice || 0,
        total_before_tax: totalBeforeTax ?? 0,
        products_discount: totalProductDiscounts,
        vat_rate: taxRate,
        delegate_id: data?.delegate_id?.value || null,
        discount_value: discountValue,
        discount_percentage: discountPercentage,
        res_name: data.res_name,
        // pm_bill_id: data.bill_number?.value,
      };

      const response = await billAPis.add({ payload: dataToSend });
      toastr.success(response.message);
      navigate(-1);
      reset(); // Reset form after successful submission
      setProductList([]);
    } catch (error) {
      console.log("error", error);
      handleBackendErrors({ error, setError });

      // Scroll to the top to show errors
      window.scrollTo(0, 0);
    }
  };

  const columns = [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.product"),
      accessor: "product_name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: [1, 5, 6, 7, 2].includes(typeFromUrl) ? t("common.quant") : "",
      accessor: "quant",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: [3, 4].includes(typeFromUrl)
        ? t("common.maintenance_quantity")
        : "",
      accessor: "maintaince_quant",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: [6, 2].includes(typeFromUrl) ? t("common.total") : "",
      accessor: "total",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: typeFromUrl === 5 ? t("bills.change_oil_bill") : "",
      accessor: "change_oil",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: typeFromUrl === 6 ? t("common.gift") : "",
      accessor: "gift",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: [6, 2].includes(typeFromUrl) ? t("common.price") : "",
      accessor: "price",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.note"),
      accessor: (cellProps) => (
        <div
          style={{
            display: "-webkit-box",
            WebkitLineClamp: 3,
            WebkitBoxOrient: "vertical",
            overflow: "hidden",
            textOverflow: "ellipsis",
            width: "100%",
            wordBreak: "break-word",
            lineHeight: "1.2em",
            maxHeight: "3.6em",
          }}
        >
          {cellProps?.product_note}
        </div>
      ),
      disableFilters: true,
      filterable: false,
    },
    {
      Header: type[typeFromUrl] === "Sales Bill" ? t("common.discount") : "",
      accessor: "discount_value",
      disableFilters: true,
      filterable: false,
    },
    {
      Header:
        type[typeFromUrl] === "Sales Bill" ? t("common.discount") + "%" : "",
      accessor: "discount_percentage",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: type[typeFromUrl] === "Sales Bill" ? t("common.net_price") : "",
      accessor: "net_price",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          !isShow && (
            <div className="d-flex align-items-center gap-2 justify-content-start">
              <div
                className="text-primary"
                onClick={() => {
                  setIsOpenEditModel(true);
                  handelSetUpdate(cellProps.product_id);
                }}
              >
                <i className="mdi mdi-pencil font-size-16"></i>
              </div>
              <div
                onClick={() => {
                  console.log(
                    "Delete clicked for product ID:",
                    cellProps.product_id
                  );
                  handelFilterFromProductList(cellProps.product_id);
                }}
                className="text-danger"
              >
                <i className="mdi mdi-trash-can font-size-16"></i>
              </div>
            </div>
          )
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ];

  const rowData = useMemo(
    () =>
      productList.length > 0
        ? productList
            .map((item, index) => ({
              product_id: item.product_id,
              id_toShow: index + 1,
              product_name: item.product_name,
              quant: item.quant,
              gift: item.gift || "----",
              price: item.price,
              change_oil: changeOilOptions[item.change_oil]?.label,
              total: item.total,
              maintaince_quant: item.maintaince_quant,
              product_note: item.notes,
              discount_percentage: `${item.discount_percentage}%`,
              discount_value: `${item.discount_value}`,
              discount: `${item.discount}`,
              net_price: `${item.net_price}`,
            }))
            .reverse()
        : [],
    [productList, i18n.language]
  );

  useEffect(() => {
    if (productList.length > 0 && (typeFromUrl === 6 || typeFromUrl === 2)) {
      let currentTotal = 0;
      let currentDiscount = 0;

      productList.forEach((item) => {
        currentTotal += Number(item.total) || 0;
      });

      productList.forEach((item) => {
        // Try to use discount_value first, then fall back to discount for backward compatibility
        const discountAmount =
          Number(item.discount_value) || Number(item.discount) || 0;
        currentDiscount += discountAmount;
        // console.log("Item discount:", item.product_name, discountAmount);
      });

      if (typeFromUrl === 4 || typeFromUrl === 3) {
        return;
      }

      // Set the total and trigger recalculation
      setValue("total", currentTotal);
      setValue("total_discounts", currentDiscount);

      // Force recalculation of totals to update selectedTotalPrice
      setTimeout(() => {
        recalculateTotals();
      }, 0);
    }
  }, [productList, typeFromUrl]);

  useEffect(() => {
    if (typeFromUrl > 0 && bilTypes?.result) {
      const selectedBillType = bilTypes.result.find(
        (item) => item.id === typeFromUrl
      );
      if (selectedBillType) {
        setSelectedProductType(selectedBillType?.product_types || []);
      }
    }
  }, [typeFromUrl, bilTypes?.result]);

  useEffect(() => {
    if (openAddModal && !isEditMode) {
      const _selectedProductId = products?.result?.find(
        (item) => item.id === watch("product_id")?.value
      );
      if (_selectedProductId?.price) {
        setValue("product_price", _selectedProductId?.price);
      }
      setSelectedQuant(_selectedProductId?.quant);
    }
  }, [openAddModal, watch("product_id"), isEditMode]);

  useEffect(() => {
    if (Number(watch("bill_type_id")) > 0) {
      const _selectedBillTypes = bilTypes?.result?.find(
        (item) => item.type === Number(watch("bill_type_id"))
      );
      setSelectedProductType(_selectedBillTypes?.product_types);
    }
  }, [watch("bill_type_id"), openAddModal, bilTypes?.result]);

  // inside your component:
  const discountPercentage = useWatch({
    control,
    name: "product_discount_percentage",
  });
  const discountValue = useWatch({ control, name: "product_discount_value" });
  const total = useWatch({ control, name: "product_total" });

  // Refs to prevent circular updates
  const lastUpdated = useRef(null);

  // When percentage changes, update value
  useEffect(() => {
    if (lastUpdated.current === "value") return; // skip if value was last updated
    const percentage = Number(discountPercentage);
    const totalValue = Number(total);

    if (!percentage && percentage !== 0) return;

    if (percentage >= 0 && percentage <= 100 && totalValue > 0) {
      const newValue = Math.round((percentage * totalValue) / 100);
      lastUpdated.current = "percentage";
      setValue("product_discount_value", newValue > 0 ? newValue : null, {
        shouldValidate: true,
      });
    }
  }, [discountPercentage]);

  // When value changes, update percentage
  useEffect(() => {
    if (lastUpdated.current === "percentage") return; // skip if percentage was last updated
    const value = Number(discountValue);
    const totalValue = Number(total);

    if (!value && value !== 0) return;

    if (value >= 0 && totalValue > 0) {
      const newPercentage = Math.round((value / totalValue) * 100);
      lastUpdated.current = "value";
      setValue(
        "product_discount_percentage",
        newPercentage > 0 ? newPercentage : null,
        {
          shouldValidate: true,
        }
      );
    }
  }, [discountValue]);

  // Reset lastUpdated after any update
  useEffect(() => {
    const timeout = setTimeout(() => {
      lastUpdated.current = null;
    }, 50);
    return () => clearTimeout(timeout);
  }, [discountPercentage]);

  const delegateOptions = useSetSelectOptions({
    data: delegates?.result,
    getOption: (item) => ({
      label: item.full_name, // Dynamic label property
      value: item.id, // Assuming there's an id
    }),
  });

  useEffect(() => {
    if (vat?.data && vat?.data?.vat_status === 1 && !selectId) {
      setValue("vat_rate", vat.data.vat_rate);
    }
  }, [vat?.data, selectId]);

  const lastChanged = useRef(null);

  useEffect(() => {
    const percentage = Number(watch("total_discount_percentage"));
    const value = Number(watch("total"));
    const rows_discount = Number(watch("total_discounts"));
    const total = value - rows_discount;
    if (!initialDataLoaded || lastChanged.current !== "percentage") return;

    if (percentage === 0 || isNaN(percentage)) {
      setValue("total_discount_value", null);
      lastChanged.current = null;
      return;
    }

    if (percentage >= 0 && percentage <= 100 && total > 0 && !isResting) {
      const discountValue = (total * percentage) / 100;
      setValue("total_discount_value", Math.round(discountValue), {
        shouldValidate: true,
      });
      if (productList.length > 0) {
        recalculateTotals();
      }
      lastChanged.current = "percentage";
    }
  }, [watch("total_discount_percentage"), initialDataLoaded]);

  useEffect(() => {
    const discountValue = Number(watch("total_discount_value"));
    const total = Number(watch("total")) - Number(watch("total_discounts"));

    if (!initialDataLoaded || lastChanged.current !== "value") return;

    if (discountValue === 0 || isNaN(discountValue)) {
      setValue("total_discount_percentage", null);
      lastChanged.current = null;
      return;
    }
    if (productList.length > 0) {
      recalculateTotals();
    }

    if (discountValue >= 0 && total > 0 && !isResting) {
      const percentage = (discountValue / total) * 100;
      setValue("total_discount_percentage", Math.round(percentage), {
        shouldValidate: true,
      });
      lastChanged.current = "value";
    }
  }, [watch("total_discount_value"), initialDataLoaded]);

  useEffect(() => {
    const timeout = setTimeout(() => {
      lastChanged.current = null;
    }, 100);
    return () => clearTimeout(timeout);
  }, [watch("total_discount_value"), watch("total_discount_percentage")]);

  useEffect(() => {
    if (productList.length > 0) {
      recalculateTotals({ totalValue: watch("total"), hasValue: true });
    }
  }, [watch("total")]);

  const handleTotalChange = (e) => {
    const value = e.target.value;
    setValue("total", value);

    // Recalculate totals with the new manual value
    if (value && !isNaN(Number(value))) {
      setTimeout(() => {
        recalculateTotals();
      }, 0);
    }
  };

  // Update handlers to enable calculations after user interaction
  const handleDiscountPercentageChange = (val) => {
    lastChanged.current = "percentage";
    // shouldUpdate = true;
    // lastChanged.current = "percentage";
    setInitialDataLoaded(true); // User has interacted, enable calculations
    setValue("total_discount_percentage", val);
  };

  const handleDiscountValueChange = (val) => {
    // lastChanged.current = "value";
    // shouldUpdate = true;
    lastChanged.current = "value";
    setInitialDataLoaded(true); // User has interacted, enable calculations
    setValue("total_discount_value", val);
  };

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("bills.bills")}
          addTitle={t("common.add") + " " + t("common.product")}
          handleOrderClicks={handelOpenAddModal}
          canPermission="bill.store"
          isAddOptions={!isShow}
          titleOfSection={t("bills.bills")}
          titleOfPage={
            isShow
              ? t("common.show")
              : selectId
              ? t("common.update")
              : t("common.create")
          }
        />

        <ActionSection
          isLoading={isLoading || isRefetching}
          handleSubmit={handleSubmit}
          selectId={selectId}
          UpdateFun={UpdateFun}
          addFun={addFun}
          handelCancel={handelCancel}
          isShow={isShow}
          isSubmitting={isSubmitting}
        >
          {(data?.result?.bill_number || bill_numbers) && (
            <h1 style={{ fontSize: 16 }} className="mb-4">
              {t("bills.bill_type") +
                ": " +
                typesToShow.find((item) => item.value === typeFromUrl)
                  ?.label}{" "}
              /{" "}
              {t("bills.bill_number") +
                ": " +
                (data?.result?.bill_number || bill_numbers)}
            </h1>
          )}
          <Row className="g-2">
            {fieldsNames.map(
              (field) =>
                field.showIn && (
                  <FormField
                    key={field.id}
                    field={field}
                    register={register}
                    errors={errors}
                    isShow={isShow}
                    isDisabled={field.disabled || field.disable}
                    t={t}
                    control={control}
                    placeholder={field.label}
                    onTotalChange={handleTotalChange}
                  />
                )
            )}

            <Col xs={2}>
              <CustomSelect
                control={control}
                error={errors.client_id}
                label={t("common.client")}
                options={clientOptions}
                name="client_id"
                isDisabled={isShow || selectId}
              />
            </Col>

            <Col xs={2}>
              <CustomSelect
                control={control}
                error={errors.delegate_id}
                label={t("common.delegate")}
                options={delegateOptions}
                name="delegate_id"
                isDisabled={isShow}
              />
            </Col>

            {textAreaField.map((field) => (
              <Col key={field.id} xs={5}>
                <TextAreaField
                  name="notes"
                  control={control}
                  placeholder={t("common.note")}
                  defaultValue=""
                  className="mb-2"
                  rows={1}
                  disabled={isShow}
                  error={errors?.notes}
                />
              </Col>
            ))}

            {typeFromUrl === 7 && (
              <Col xs={2} className="mt-2">
                <CustomSelect
                  control={control}
                  label={t("common.contract")}
                  options={optionContract}
                  name="contract_id"
                  isDisabled={isShow}
                />
              </Col>
            )}

            <TableContainer
              hideSHowGFilter={true}
              columns={columns || []}
              data={rowData}
              hidePagination
              isPagination={true}
              isAddOptions={!isShow}
              iscustomPageSize={true}
              isBordered={true}
              isSmall
              canPermission="bill.add"
              customPageSize={10}
              className="custom-header-css table align-middle table-nowrap"
              tableClassName="table-centered align-middle table-nowrap mb-0"
              theadClassName="text-muted table-light"
            />
            {totalsFields.map(
              (field) =>
                field.showIn && (
                  <FormField
                    key={field.id}
                    field={field}
                    register={register}
                    errors={errors}
                    isShow={isShow}
                    isDisabled={field.disabled || field.disable}
                    t={t}
                    control={control}
                    placeholder={field.label}
                    onTotalChange={handleTotalChange}
                  />
                )
            )}

            {typeFromUrl === 6 && (
              <Col xs={2}>
                <Input
                  control={control}
                  name="total_discount_value"
                  isDisabled={isShow}
                  placeholder={t("bills.discount_value")}
                  type="number"
                  min={0}
                  error={errors?.total_discount_value}
                  // onChange={handleDiscountValueChange}
                  isOnChange={handleDiscountValueChange}
                />
              </Col>
            )}
            {typeFromUrl === 6 && (
              <Col xs={2}>
                <Input
                  control={control}
                  name="total_discount_percentage"
                  isDisabled={isShow}
                  placeholder={t("bills.discount_percentage")}
                  type="number"
                  min={0}
                  max={100}
                  error={errors?.total_discount_percentage}
                  // onChange={handleDiscountPercentageChange}
                  isOnChange={handleDiscountPercentageChange}
                />
              </Col>
            )}
            <Col xs={12} className="mt-2">
              <Row>
                {/* VAT Input Field */}
                {(typeFromUrl === 7 ||
                  typeFromUrl === 4 ||
                  typeFromUrl === 6 ||
                  typeFromUrl === 2) &&
                  ((!selectId && vat?.data?.vat_status === 1) ||
                    (selectId && data?.result?.vat_rate > 0)) && (
                    <Col xs={2} className="mb-2">
                      <Input
                        control={control}
                        name="vat_rate"
                        isDisabled={true}
                        placeholder={t("common.vat_rate")}
                        type="number"
                        error={errors?.vat_rate}
                      />
                    </Col>
                  )}

                {/* Total Before Tax Input Field */}
                {(typeFromUrl === 7 ||
                  typeFromUrl === 4 ||
                  typeFromUrl === 6 ||
                  typeFromUrl === 2) &&
                  ((!selectId && vat?.data?.vat_status === 1) ||
                    (selectId && data?.result?.vat_rate > 0)) &&
                  typeFromUrl !== 7 && (
                    <Col xs={2} className="mb-2">
                      <Input
                        control={control}
                        name="total_before_tax"
                        isDisabled={true}
                        placeholder={t("bills.total_before_tax")}
                        type="number"
                      />
                    </Col>
                  )}
              </Row>
            </Col>

            {(typeFromUrl === 7 ||
              typeFromUrl === 4 ||
              typeFromUrl === 6 ||
              typeFromUrl === 2) &&
              selectedTotalPrice >= 0 && (
                <div
                  style={{
                    padding: 10,
                    borderTop: "1px solid #ccc",
                    marginTop: 20,
                    fontSize: 15,
                    fontWeight: "bold",
                  }}
                >
                  {t("common.total")}:{" "}
                  {(Number(selectedTotalPrice) || 0).toFixed(2)}
                </div>
              )}
          </Row>
        </ActionSection>
        <ProductModal
          isOpen={openAddModal}
          onClose={handelCloseAddModal}
          onAdd={handelAddProductToList}
          selectedProductId={selectedProductId}
          isEditMode={isEditMode}
        >
          <Row className="g-2">
            {productsFields.map((item) => item.showIn && item.field)}
            {typeFromUrl === 6 && (
              <Col xs={6}>
                <Input
                  control={control}
                  name="product_discount_percentage"
                  isDisabled={isShow}
                  placeholder={t("bills.discount_percentage")}
                  type="number"
                  error={errors?.product_discount_percentage}
                />
              </Col>
            )}
            {typeFromUrl === 6 && (
              <Col xs={6}>
                <Input
                  control={control}
                  name="product_discount_value"
                  isDisabled={isShow}
                  placeholder={t("bills.discount_value")}
                  type="number"
                  error={errors?.product_discount_value}
                />
              </Col>
            )}

            <Col xs={12} className="mt-2">
              <TextAreaField
                placeholder={t("common.note")}
                name="product_note"
                control={control}
                defaultValue=""
                className="mb-2"
                disabled={isShow}
              />
            </Col>
          </Row>
        </ProductModal>
      </Container>
    </div>
  );
};

export default BillsActions;
