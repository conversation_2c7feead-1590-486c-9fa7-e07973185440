import { useTranslation, withTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Container,
  Label,
  Modal,
  ModalBody,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import ClipLoader from "react-spinners/ClipLoader";
import toastr from "toastr";
import { storeOperationsApis } from "../../apis/store-operation/api";
import { useStoreOperationQueries } from "../../apis/store-operation/query";
import { Link, useLocation } from "react-router-dom";
import { handleBackendErrors, truncateText } from "../../helpers/api_helper";
import { Can } from "../../components/permissions-way/can";
import CustomInput from "../../components/Common/Input";
import CustomSelect from "../../components/Common/Select";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";
const StoreOperation = () => {
  const [page, setPage] = useState(1);
  const { t, i18n } = useTranslation();
  const {
    data: ContractTypes,
    isLoading: isLoadingUsers,
    refetch,
  } = useStoreOperationQueries.useGetAllOperation({ limit: 10, page: page });
  const { pathname } = useLocation();
  const [selectId, setSelectId] = useState(null);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [isShow, setIsShow] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const { data: contractType, isLoading: isLoadingContractType } =
    useStoreOperationQueries.useGetStoreOperation({
      id: selectId,
    });

  const [openSidebar, setOPenSideBar] = useState(false);
  const handelOpenSideBar = () => {
    setOPenSideBar(true);
  };

  const statsusOption = useMemo(
    () => [
      {
        value: "1",
        label: t("types.store_operation.input"),
      },
      {
        value: "2",
        label: t("types.store_operation.expulsion"),
      },
    ],
    [t, i18n.language]
  );

  const {
    control,
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    setError,
    watch,
    clearErrors,
  } = useForm({
    defaultValues: {
      titleArabic: "",
      titleEnglish: "",
      type: {
        value: "1",
        label: t("types.store_operation.input"),
      },
    },
    // resolver: yupResolver(schema),
  });

  // Reset form when language changes
  useEffect(() => {
    reset({
      titleArabic: "",
      titleEnglish: "",
      type: {
        value: "1",
        label: t("types.store_operation.input"),
      },
    });
  }, [i18n.language, t, reset]);

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoadingContractType && contractType?.result) {
      // Populate form with role data when loaded
      reset({
        titleArabic: contractType?.result?.title.ar,
        titleEnglish: contractType?.result?.title.en,
        type: {
          value: contractType?.result.type,
          label: statsusOption.find(
            (item) => item.value === contractType?.result?.type
          )?.label,
        },
      });
    }
  }, [selectId, isLoadingContractType, contractType]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };
  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setSelectId(null);
    setIsShow(false);
    reset({ titleArabic: "", titleEnglish: "" });
  };

  const handelCloseSideBar = () => {
    setOPenSideBar(false);
    reset({ titleArabic: "", titleEnglish: "" });
    setSelectId(null);
    setIsShow(false);
  };
  const handelSelectId = (id) => {
    setSelectId(id);
  };

  const UpdateFun = async (data) => {
    try {
      clearErrors();
      const response = await storeOperationsApis.update({
        title: {
          en: data.titleEnglish ? data.titleEnglish : data.titleArabic,
          ar: data.titleArabic ? data.titleArabic : data.titleEnglish,
        },
        type: data.type.value,
        id: selectId,
      });
      refetch();
      handelCLoseModal();
      toastr.success(response.message);
      handelCloseSideBar();
    } catch (error) {
      handleBackendErrors({ error, setError });
      clearErrors();
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  const addFun = async (data) => {
    try {
      clearErrors();
      const response = await storeOperationsApis.add({
        title: {
          en: data.titleEnglish ? data.titleEnglish : data.titleArabic,
          ar: data.titleArabic ? data.titleArabic : data.titleEnglish,
        },
        type: data.type.value,
      });
      refetch();
      toastr.success(response.message);
      handelCloseSideBar();
      handelCLoseModal();
    } catch (error) {
      handleBackendErrors({ error, setError });
      clearErrors();
    }
    // Call API with selected permissions (data.permissions)
  };

  const breadcrumbItems = [
    {
      title: t("types.store_operation.store_operation"),
      link: pathname,
    },
    // { title: "list", link: pathname },
  ];

  const columns = [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.title"),
      width: 50,
      accessor: "title",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.type"),
      accessor: "type",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2">
            <Can permission={"store_operation.update"}>
              <Link
                to={cellProps.is_default !== 1 && `#`}
                className="text-primary"
                onClick={() => {
                  handelSelectId(cellProps.id);
                  // setSelectId(cellProps.id);
                  handelOpenSideBar();
                }}
              >
                {/* <i className="mdi mdi-pencil font-size-18"></i> */}
                <FaPenToSquare size={14} />
              </Link>
            </Can>
            <Can permission={"store_operation.destroy"}>
              <Link
                onClick={() => {
                  if (cellProps.isDefault !== 1) {
                    handelOpenModal();
                    handelSelectId(cellProps.id);
                  }
                }}
                to="#"
                className="text-danger"
              >
                {/* <i className="mdi mdi-trash-can font-size-18"></i> */}
                <MdDeleteSweep size={18} />
              </Link>
            </Can>
            <Can permission={"store_operation.show"}>
              <Link
                onClick={() => {
                  handelSelectId(cellProps.id);
                  // setSelectId(cellProps.id);
                  handelOpenSideBar();
                  setIsShow(true);
                }}
                className="text-success"
              >
                {/* <i className=" ri-information-fill font-size-16"></i> */}
                <FaInfoCircle size={14} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ];

  const rowData = useMemo(
    () =>
      ContractTypes?.result?.length > 0
        ? ContractTypes.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: index + 1,
              title:
                i18n.language === "eng"
                  ? truncateText({
                      text:
                        i18n.language === "eng" ? item.title.en : item.title.ar,
                      maxLengthPercent: 0.3,
                    })
                  : truncateText({
                      text:
                        i18n.language === "en" ? item.title.en : item.title.ar,
                      maxLengthPercent: 0.3,
                    }) || "----",
              type:
                Number(item.type) === 1
                  ? t("types.store_operation.input")
                  : t("types.store_operation.expulsion"),
            }))
            .reverse()
        : [],
    [ContractTypes?.result, t]
  );

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await storeOperationsApis.deleteApi({
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      // toastr.error(error?.response?.data?.message);
      handleBackendErrors({ error, setError });
    }
    // Call API with selected permissions (data.permissions)
  };

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("types.store_operation.store_operation")}
          breadcrumbItems={breadcrumbItems}
          addTitle={
            t("common.add") + " " + t("types.store_operation.store_operation")
          }
          isAddOptions
          canPermission={"store_operation.store"}
          handleOrderClicks={handelOpenSideBar}
        />
        <div>
          <Modal
            isOpen={openSidebar}
            toggle={handelCloseSideBar}
            backdrop="static"
            fade={false}
          >
            <ModalHeader toggle={handelCloseSideBar}>
              {isShow
                ? t("common.show") +
                  " " +
                  t("types.store_operation.stores_operation")
                : selectId > 0
                ? t("common.update") +
                  " " +
                  t("types.store_operation.stores_operation")
                : t("common.add") +
                  " " +
                  t("types.store_operation.stores_operation")}
            </ModalHeader>
            <ModalBody>
              <form onSubmit={handleSubmit(selectId > 0 ? UpdateFun : addFun)}>
                {isLoadingContractType ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <>
                    <div className="mb-4">
                      <CustomInput
                        name="titleEnglish"
                        control={control}
                        label={t("common.title_in_english")}
                        type="text"
                        placeholder={t("common.title_in_english")}
                        disabled={isShow}
                        error={errors.titleEnglish}
                        rules={{ required: t("common.field_required") }}
                      />
                    </div>
                    <div className="mb-4">
                      <div className="mb-4">
                        <CustomInput
                          name="titleArabic"
                          control={control}
                          label={t("common.title_in_arabic")}
                          type="text"
                          placeholder={t("common.title_in_arabic")}
                          disabled={isShow}
                          error={errors.titleArabic}
                        />
                      </div>
                      <div className="mb-2">
                        <CustomSelect
                          name="type"
                          control={control}
                          label={t("common.type")}
                          options={statsusOption}
                          placeholder={t("common.type")}
                          isDisabled={isShow || selectId}
                          error={errors.type}
                        />
                      </div>
                      {errors.type?.message && (
                        <p className="invalid-feedback">
                          {errors.type?.message}
                        </p>
                      )}
                    </div>
                  </>
                )}
                <ModalFooter>
                  <Button
                    type="button"
                    color="light"
                    onClick={handelCloseSideBar}
                    className="btn-sm "
                    style={{ height: "32px", width: "54px" }}
                  >
                    {t("common.close")}
                  </Button>
                  {!isShow && (
                    <Button
                      color="primary"
                      className="btn-sm waves-effect waves-light primary-button"
                      type="submit"
                      disabled={
                        isSubmitting ||
                        !(
                          (watch("titleArabic") || watch("titleEnglish")) &&
                          watch("type")?.value
                        )
                      }
                    >
                      {isSubmitting ? (
                        <ClipLoader color="white" size={15} />
                      ) : selectId ? (
                        t("common.update")
                      ) : (
                        t("common.add")
                      )}
                    </Button>
                  )}
                </ModalFooter>
              </form>
            </ModalBody>
          </Modal>
        </div>
        <Card>
          <CardBody>
            {isLoadingUsers ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : (
              <TableContainer
                hideSHowGFilter={false}
                columns={columns || []}
                data={rowData || []}
                isPagination={true}
                handleOrderClicks={handelOpenSideBar}
                iscustomPageSize={true}
                isBordered={true}
                pageSize={10}
                pageIndex={page}
                manualPagination={true}
                pageCount={ContractTypes?.meta?.last_page || 1}
                currentPage={page}
                setPage={setPage}
              />
            )}
          </CardBody>
        </Card>
        <Modal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          backdrop="static"
          fade={false}
        >
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.delete")} {t("types.store_operation.store_operation")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button type="button" color="light" onClick={handelCLoseModal}>
                {t("common.close")}
              </Button>
              <Button
                disabled={isDeleting}
                onClick={DeleteFun}
                type="button"
                color="danger"
              >
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.delete")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
      </Container>
    </div>
  );
};
export default withTranslation()(StoreOperation);
